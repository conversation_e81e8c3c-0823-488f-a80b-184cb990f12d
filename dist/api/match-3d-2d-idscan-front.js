!function(e,o){"object"==typeof exports&&"object"==typeof module?module.exports=o():"function"==typeof define&&define.amd?define("api/match-3d-2d-idscan-front",[],o):"object"==typeof exports?exports["api/match-3d-2d-idscan-front"]=o():e["api/match-3d-2d-idscan-front"]=o()}(this,(()=>{return e={137:e=>{async function o(e,o){if("POST"!==e.method)return o.status(405).json({error:"Method not allowed"});try{const s=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/match-3d-2d-idscan/front`,r={"Content-Type":"application/json",Accept:"application/json",Authorization:e.headers.authorization||(process.env.JWT_TOKEN?`Bearer ${process.env.JWT_TOKEN}`:void 0),"X-Device-Key":e.headers["x-device-key"],"X-User-Agent":e.headers["x-user-agent"],"X-Session-Id":e.headers["x-session-id"],"X-Tid":e.headers["x-tid"],"X-Ekyc-Token":e.headers["x-ekyc-token"],"X-Ekyc-Sdk-Version":e.headers["x-ekyc-sdk-version"]||"1.0.0",correlationid:e.headers.correlationid};Object.keys(r).forEach((e=>{void 0===r[e]&&delete r[e]})),console.log("Front ID scan request to:",s),console.log("Request headers:",Object.keys(r));const t=await fetch(s,{method:"POST",headers:r,body:JSON.stringify(e.body)}),n=await t.json();if(console.log("Front ID scan response status:",t.status),console.log("Front ID scan response code:",n.code),t.ok&&"CUS-KYC-1000"===n.code){const e={wasProcessed:!0,error:!1,scanResultBlob:n.data?.scanResultBlob||"",originalResponse:n};return console.log("Returning front scan success response with scanResultBlob length:",e.scanResultBlob.length),o.status(200).json(e)}{const e={wasProcessed:!1,error:!0,errorMessage:n.description||n.message||"Unknown error occurred during front ID scan",originalResponse:n};return console.log("Returning front scan error response:",e.errorMessage),o.status(t.status).json(e)}}catch(e){return console.error("Error processing front ID scan:",e),o.status(500).json({wasProcessed:!1,error:!0,errorMessage:"Failed to process front ID scan"})}}e.exports=o,e.exports.default=o}},o={},function s(r){var t=o[r];if(void 0!==t)return t.exports;var n=o[r]={exports:{}};return e[r](n,n.exports,s),n.exports}(137);var e,o}));