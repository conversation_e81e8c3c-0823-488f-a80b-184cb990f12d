const IDScanDataSource = require('../datasources/IDScanDataSource');
const IDScanFrontDataSource = require('../datasources/IDScanFrontDataSource');
const IDScanBackDataSource = require('../datasources/IDScanBackDataSource');
const LivenessCheckDataSource = require('../datasources/LivenessCheckDataSource');

/**
 * Repository for FaceTec related operations
 * Implements the repository pattern to abstract data access
 */
class FaceTecRepository {
    constructor() {
        this.idScanDataSource = new IDScanDataSource();
        this.idScanFrontDataSource = new IDScanFrontDataSource();
        this.idScanBackDataSource = new IDScanBackDataSource();
        this.livenessCheckDataSource = new LivenessCheckDataSource();
    }

    /**
     * Submit ID scan data for processing
     * @param {Object} scanData - The scan data including images and metadata
     * @param {Object} headers - Additional headers for the request
     * @param {Function} onProgress - Progress callback function
     * @returns {Promise<Object>} - Processed response
     */
    async submitIDScan(scanData, headers = {}, onProgress = null) {
        try {
            // Call the data source to post ID scan data
            const response = await this.idScanDataSource.postIDScanOnly(scanData, headers, onProgress);

            // Repository can add additional business logic here if needed
            // For example: data transformation, caching, etc.

            return response;
        } catch (error) {
            console.error('FaceTecRepository - submitIDScan error:', error);
            throw error;
        }
    }

    /**
     * Submit front ID scan data for processing
     * @param {Object} scanData - The front scan data including front image and metadata
     * @param {Object} headers - Additional headers for the request
     * @param {Function} onProgress - Progress callback function
     * @returns {Promise<Object>} - Processed response
     */
    async submitIDScanFront(scanData, headers = {}, onProgress = null) {
        try {
            // Call the front data source to post front ID scan data
            const response = await this.idScanFrontDataSource.postIDScanFront(scanData, headers, onProgress);

            // Repository can add additional business logic here if needed
            // For example: data transformation, caching, etc.

            return response;
        } catch (error) {
            console.error('FaceTecRepository - submitIDScanFront error:', error);
            throw error;
        }
    }

    /**
     * Submit back ID scan data for processing
     * @param {Object} scanData - The back scan data including back image and metadata
     * @param {Object} headers - Additional headers for the request
     * @param {Function} onProgress - Progress callback function
     * @returns {Promise<Object>} - Processed response
     */
    async submitIDScanBack(scanData, headers = {}, onProgress = null) {
        try {
            // Call the back data source to post back ID scan data
            const response = await this.idScanBackDataSource.postIDScanBack(scanData, headers, onProgress);

            // Repository can add additional business logic here if needed
            // For example: data transformation, caching, etc.

            return response;
        } catch (error) {
            console.error('FaceTecRepository - submitIDScanBack error:', error);
            throw error;
        }
    }

    /**
     * Submit liveness check data for processing
     * @param {Object} livenessData - The liveness data including face scan and audit trail
     * @param {Object} headers - Additional headers for the request
     * @param {Function} onProgress - Progress callback function
     * @returns {Promise<Object>} - Processed response
     */
    async submitLivenessCheck(livenessData, headers = {}, onProgress = null) {
        try {
            // Call the data source to post liveness check data
            const response = await this.livenessCheckDataSource.postLivenessCheck(livenessData, headers, onProgress);
            
            // Repository can add additional business logic here if needed
            // For example: data transformation, caching, etc.
            
            return response;
        } catch (error) {
            console.error('FaceTecRepository - submitLivenessCheck error:', error);
            throw error;
        }
    }


    /**
     * Validate scan data before submission
     * @param {Object} scanData - The scan data to validate
     * @returns {boolean} - True if valid, throws error if invalid
     */
    validateScanData(scanData) {
        if (!scanData) {
            throw new Error('Scan data is required');
        }

        if (!scanData.idScan) {
            throw new Error('ID scan data is required');
        }

        // Add more validation rules as needed
        return true;
    }

    /**
     * Validate front scan data before submission
     * @param {Object} scanData - The front scan data to validate
     * @returns {boolean} - True if valid, throws error if invalid
     */
    validateFrontScanData(scanData) {
        return this.idScanFrontDataSource.validateFrontScanData(scanData);
    }

    /**
     * Validate back scan data before submission
     * @param {Object} scanData - The back scan data to validate
     * @returns {boolean} - True if valid, throws error if invalid
     */
    validateBackScanData(scanData) {
        return this.idScanBackDataSource.validateBackScanData(scanData);
    }


    /**
     * Validate liveness data before submission
     * @param {Object} livenessData - The liveness data to validate
     * @returns {boolean} - True if valid, throws error if invalid
     */
    validateLivenessData(livenessData) {
        if (!livenessData) {
            throw new Error('Liveness data is required');
        }

        if (!livenessData.faceScan) {
            throw new Error('Face scan data is required');
        }

        // Add more validation rules as needed
        return true;
    }

}

module.exports = FaceTecRepository;
