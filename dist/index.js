!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("index",[],t):"object"==typeof exports?exports.index=t():e.index=t()}(this,(()=>{return e={0:(e,t,s)=>{const o=s(216),n=s(599),r=s(719);e.exports=class{constructor(){this.faceTecRepository=new o}async execute({idScanResult:e,deviceKey:t,additionalHeaders:s={},onProgress:o=null}){const n=performance.now();try{r.logMessage("Starting PostIDScanOnlyUseCase execution"),r.logMessage("Preparing scan data...");const a=this.prepareScanData(e);r.logData("Scan Data Keys",Object.keys(a)),r.logMessage("Preparing headers...");const i=this.prepareHeaders(e,t,s);r.logData("Request Headers",Object.keys(i)),r.logMessage("Validating scan data..."),this.faceTecRepository.validateScanData(a),r.logSuccess("Scan data validation passed"),r.logMessage("Submitting to repository...");const c=await this.faceTecRepository.submitIDScan(a,i,o);r.logMessage("Processing response...");const l=this.processResponse(c),d=performance.now();return r.logPerformance("PostIDScanOnlyUseCase.execute",n,d),r.logSuccess(`UseCase completed successfully: ${l.success}`),l}catch(e){const t=performance.now();throw r.logPerformance("PostIDScanOnlyUseCase.execute (failed)",n,t),r.logError("PostIDScanOnlyUseCase - execute error",e),e}}prepareScanData(e){const t={idScan:e.idScan,enableConfirmInfo:!0};return e.frontImages&&e.frontImages[0]&&(t.idScanFrontImage=e.frontImages[0]),e.backImages&&e.backImages[0]&&(t.idScanBackImage=e.backImages[0]),t}prepareHeaders(e,t,s){const o={};return t&&(o["X-Device-Key"]=t),e.sessionId&&(o["X-User-Agent"]=FaceTecSDK.createFaceTecAPIUserAgentString(e.sessionId)),s.Authorization&&(o.Authorization=s.Authorization),s["X-Session-Id"]&&(o["X-Session-Id"]=s["X-Session-Id"]),s["X-Ekyc-Token"]&&(o["X-Ekyc-Token"]=s["X-Ekyc-Token"]),s.correlationid&&(o.correlationid=s.correlationid),o["X-Tid"]=n.getUniqueId(),o}processResponse(e){let t=null;if(e.originalResponse&&e.originalResponse.data){const s=e.originalResponse.data;t={nationalId:s.nationalId,titleTh:s.titleTh,firstNameTh:s.firstNameTh,middleNameTh:s.middleNameTh,lastNameTh:s.lastNameTh,titleEn:s.titleEn,firstNameEn:s.firstNameEn,middleNameEn:s.middleNameEn,lastNameEn:s.lastNameEn,dateOfBirth:s.dateOfBirth,dateOfIssue:s.dateOfIssue,dateOfExpiry:s.dateOfExpiry,laserId:s.laserId},r.logData("Extracted OCR Data",Object.keys(t))}else r.logMessage("No OCR data found in response");return{success:!0===e.wasProcessed&&!1===e.error,scanResultBlob:e.scanResultBlob,originalResponse:e.originalResponse,errorMessage:e.errorMessage,userOcrValue:t}}}},103:e=>{e.exports=class{constructor(e){this.data=e}getToken(){return this.data?.token||null}getEkycToken(){return this.data?.data?.ekycToken?this.data.data.ekycToken:this.data?.ekycToken||null}getExpiresAt(){return this.data?.expiresAt||null}getCode(){return this.data?.code||null}getDescription(){return this.data?.description||null}isValid(){return!!this.getEkycToken()}toJSON(){return this.data}}},161:(e,t,s)=>{const o=s(103);e.exports=class{constructor(e){this.authApiDataSource=e}async getSessionToken(e={}){const t=await this.authApiDataSource.getSessionToken(e);return new o(t)}async getFaceTecSessionTokenWithEkycToken(e={}){const t=await this.authApiDataSource.getFaceTecSessionTokenWithEkycToken(e);return new o(t)}}},189:e=>{e.exports=class{constructor(e,t="USD"){this.amount=e,this.currencyCode=t}format(){return new Intl.NumberFormat("en-US",{style:"currency",currency:this.currencyCode}).format(this.amount)}getAmount(){return this.amount}getCurrencyCode(){return this.currencyCode}}},216:(e,t,s)=>{const o=s(518),n=s(777);e.exports=class{constructor(){this.idScanDataSource=new o,this.livenessCheckDataSource=new n}async submitIDScan(e,t={},s=null){try{return await this.idScanDataSource.postIDScanOnly(e,t,s)}catch(e){throw console.error("FaceTecRepository - submitIDScan error:",e),e}}async submitLivenessCheck(e,t={},s=null){try{return await this.livenessCheckDataSource.postLivenessCheck(e,t,s)}catch(e){throw console.error("FaceTecRepository - submitLivenessCheck error:",e),e}}validateScanData(e){if(!e)throw new Error("Scan data is required");if(!e.idScan)throw new Error("ID scan data is required");return!0}validateLivenessData(e){if(!e)throw new Error("Liveness data is required");if(!e.faceScan)throw new Error("Face scan data is required");return!0}}},266:(e,t,s)=>{const o=s(307),n=s(719);e.exports=function(e,t,s,r){var a=this;this.latestNetworkRequest=new XMLHttpRequest,this.deviceKey=s||null,this.additionalHeaders=r||{},this.postLivenessCheckUseCase=new o,this.processSessionResultWhileFaceTecSDKWaits=function(e,t){if(a.latestSessionResult=e,e.status!==FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully)return a.latestNetworkRequest.abort(),a.latestNetworkRequest=new XMLHttpRequest,void t.cancel();a.executeLivenessCheckUseCase(e,t)},this.executeLivenessCheckUseCase=async function(e,t){try{const s=function(e){t.uploadProgress(e)},o=await a.postLivenessCheckUseCase.execute({sessionResult:e,deviceKey:a.deviceKey,additionalHeaders:a.additionalHeaders,onProgress:s});o.success?(FaceTecSDK.FaceTecCustomization.setOverrideResultScreenSuccessMessage("Face Scanned\n3D Liveness Proven"),t.proceedToNextStep(o.scanResultBlob)):a.cancelDueToNetworkError(o.errorMessage||"Unexpected API response, cancelling out.",t)}catch(e){console.error("LivenessCheckProcessor - executeLivenessCheckUseCase error:",e),a.cancelDueToNetworkError(e.message||"Exception while handling API response, cancelling out.",t)}},this.onFaceTecSDKCompletelyDone=function(){null!==a.latestSessionResult&&(a.success=a.latestSessionResult.isCompletelyDone),a.success&&n.logMessage("Liveness Check Complete"),a.sampleAppControllerReference.onComplete(a.latestSessionResult,200)},this.cancelDueToNetworkError=function(e,t){!1===a.cancelledDueToNetworkError&&(console.error(e),a.cancelledDueToNetworkError=!0,t.cancel())},this.isSuccess=function(){return a.success},this.success=!1,this.sampleAppControllerReference=t,this.latestSessionResult=null,this.cancelledDueToNetworkError=!1,new FaceTecSDK.FaceTecSession(this,e)}},307:(e,t,s)=>{const o=s(216);e.exports=class{constructor(){this.faceTecRepository=new o}async execute(e){try{const{sessionResult:t,deviceKey:o,additionalHeaders:n,onProgress:r}=e;console.log("=== POST LIVENESS CHECK USE CASE - STARTING ==="),console.log("Input params:",{hasSessionResult:!!t,deviceKey:o,additionalHeaders:n,hasOnProgress:"function"==typeof r}),t&&console.log("Session result details:",{sessionId:t.sessionId,hasFaceScan:!!t.faceScan,hasAuditTrail:!!t.auditTrail&&t.auditTrail.length>0,hasLowQualityAuditTrail:!!t.lowQualityAuditTrail&&t.lowQualityAuditTrail.length>0,auditTrailLength:t.auditTrail?t.auditTrail.length:0,lowQualityAuditTrailLength:t.lowQualityAuditTrail?t.lowQualityAuditTrail.length:0});const a={...n};if(o&&(a["X-Device-Key"]=o),t.sessionId&&"undefined"!=typeof FaceTecSDK&&(a["X-User-Agent"]=FaceTecSDK.createFaceTecAPIUserAgentString(t.sessionId)),a["X-Tid"])console.log("✅ X-Tid header already present:",a["X-Tid"]);else{const e=s(599);a["X-Tid"]=e.getUniqueId(),console.log("🔧 Generated X-Tid header:",a["X-Tid"])}console.log("Prepared headers:",JSON.stringify(a,null,2));const i={faceScan:t.faceScan,auditTrailImage:t.auditTrail[0],lowQualityAuditTrailImage:t.lowQualityAuditTrail[0],sessionId:t.sessionId,function:"liveness"};console.log("=== POST LIVENESS CHECK USE CASE - PREPARED DATA ==="),console.log("Liveness data keys:",Object.keys(i)),console.log("Liveness data structure:",{function:i.function,sessionId:i.sessionId,hasFaceScan:!!i.faceScan,hasAuditTrailImage:!!i.auditTrailImage,hasLowQualityAuditTrailImage:!!i.lowQualityAuditTrailImage,faceScanLength:i.faceScan?i.faceScan.length:0,auditTrailImageLength:i.auditTrailImage?i.auditTrailImage.length:0,lowQualityAuditTrailImageLength:i.lowQualityAuditTrailImage?i.lowQualityAuditTrailImage.length:0}),console.log("Calling repository.submitLivenessCheck...");const c=await this.faceTecRepository.submitLivenessCheck(i,a,r);if(console.log("=== POST LIVENESS CHECK USE CASE - REPOSITORY RESPONSE ==="),console.log("Repository response:",JSON.stringify(c,null,2)),!0===c.wasProcessed&&!1===c.error){const e={success:!0,scanResultBlob:c.scanResultBlob};return console.log("✅ USE CASE SUCCESS - Returning:",e),e}{const e={success:!1,errorMessage:c.errorMessage||"Server returned an error."};return console.log("❌ USE CASE ERROR - Returning:",e),e}}catch(e){throw console.error("❌ PostLivenessCheckUseCase error:",e),e}}}},345:e=>{e.exports=class{static validateAll(e){if(!e)return{isValid:!1,errors:["No OCR data provided"],fieldValidations:{}};const t={},s=[],o=this.validateNationalId(e.nationalId);t.nationalId=o,o.isValid||s.push(`National ID: ${o.error}`),t.titleTh=this.validateRequiredField(e.titleTh,"Title (TH)"),t.firstNameTh=this.validateRequiredField(e.firstNameTh,"First Name (TH)"),t.middleNameTh=this.validateOptionalField(e.middleNameTh,"Middle Name (TH)"),t.lastNameTh=this.validateRequiredField(e.lastNameTh,"Last Name (TH)"),t.titleEn=this.validateRequiredField(e.titleEn,"Title (EN)"),t.firstNameEn=this.validateRequiredField(e.firstNameEn,"First Name (EN)"),t.middleNameEn=this.validateOptionalField(e.middleNameEn,"Middle Name (EN)"),t.lastNameEn=this.validateRequiredField(e.lastNameEn,"Last Name (EN)"),t.dateOfBirth=this.validateDate(e.dateOfBirth,"Date of Birth"),t.dateOfIssue=this.validateDate(e.dateOfIssue,"Date of Issue"),t.dateOfExpiry=this.validateDate(e.dateOfExpiry,"Date of Expiry"),t.laserId=this.validateLaserId(e.laserId),Object.values(t).forEach((e=>{!e.isValid&&e.error&&s.push(e.error)}));const n=this.validateIdCardExpiry(e.dateOfExpiry);return n.isValid||s.push(n.error),{isValid:0===s.length,errors:s,fieldValidations:t,isExpired:!n.isValid}}static validateNationalId(e){if(!e||"string"!=typeof e)return{isValid:!1,error:"National ID is required",status:"missing"};const t=e.replace(/[\s-]/g,"");return/^\d{13}$/.test(t)?this.validateThaiIdChecksum(t)?{isValid:!0,status:"valid"}:{isValid:!1,error:"Invalid National ID checksum",status:"invalid"}:{isValid:!1,error:"National ID must be 13 digits",status:"invalid"}}static validateThaiIdChecksum(e){if(13!==e.length)return!1;let t=0;for(let s=0;s<12;s++)t+=parseInt(e[s])*(13-s);const s=t%11;return(s<2?s:11-s)===parseInt(e[12])}static validateRequiredField(e,t){return e&&"string"==typeof e&&""!==e.trim()?{isValid:!0,status:"valid"}:{isValid:!1,error:`${t} is required`,status:"missing"}}static validateOptionalField(e,t){return e&&"string"==typeof e&&""!==e.trim()?{isValid:!0,status:"valid"}:{isValid:!0,status:"optional"}}static validateDate(e,t){if(!e||"string"!=typeof e)return{isValid:!1,error:`${t} is required`,status:"missing"};const s=this.parseDate(e);return!s||isNaN(s.getTime())?{isValid:!1,error:`${t} has invalid date format`,status:"invalid"}:{isValid:!0,status:"valid",parsedDate:s}}static parseDate(e){if(!e)return null;const t=[/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,/^(\d{1,2})-(\d{1,2})-(\d{4})$/,/^(\d{4})-(\d{1,2})-(\d{1,2})$/,/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/];for(const s of t){const t=e.match(s);if(t){let e,o,n;s.source.startsWith("^(\\d{4})")?(n=parseInt(t[1]),o=parseInt(t[2])-1,e=parseInt(t[3])):(e=parseInt(t[1]),o=parseInt(t[2])-1,n=parseInt(t[3]));const r=new Date(n,o,e);if(r.getFullYear()===n&&r.getMonth()===o&&r.getDate()===e)return r}}return null}static validateLaserId(e){return e&&"string"==typeof e&&""!==e.trim()?/^[A-Za-z0-9]+$/.test(e.trim())?{isValid:!0,status:"valid"}:{isValid:!1,error:"Laser ID contains invalid characters",status:"invalid"}:{isValid:!1,error:"Laser ID is required",status:"missing"}}static validateIdCardExpiry(e){const t=this.validateDate(e,"Expiry Date");if(!t.isValid)return t;const s=t.parsedDate,o=new Date;return o.setHours(0,0,0,0),s<o?{isValid:!1,error:"ID card has expired",status:"expired"}:{isValid:!0,status:"valid"}}static getStatusIcon(e){switch(e){case"valid":return"✓";case"invalid":return"✗";case"missing":return"⚠";case"optional":return"○";case"expired":return"⏰";default:return"?"}}static getStatusColor(e){switch(e){case"valid":return"#4CAF50";case"invalid":return"#F44336";case"missing":return"#FF9800";case"optional":return"#9E9E9E";case"expired":return"#FF5722";default:return"#757575"}}}},347:(e,t,s)=>{const o=s(592),n=s(863),r=s(995),a=s(985),i=s(161),c=s(548),{TokenStorage:l}=s(411),{UuidGenerator:d}=s(411),u=s(382),p=new i(new c),g=new o,h=new n,f=new r(p),y=new a(p),S=s(955),k=s(453),T=async(e={},t=!0)=>{try{e["X-Session-Id"]&&l.storeSessionId(e["X-Session-Id"]);const s=await f.execute(e),o=s.toJSON();if(t){const e=s.getEkycToken();e&&l.storeEkycToken(e)}return o}catch(e){throw console.error("Error getting session token:",e),e}},I=async(e={},t=!0)=>{try{const s=l.getSessionId();s&&!e["X-Session-Id"]&&(e={...e,"X-Session-Id":s});const o=(await y.execute(e)).toJSON();if(o.faceTecInitialized=!1,t&&o&&"CUS-KYC-1000"===o.code&&o.data&&o.data.deviceKey&&o.data.encryptionKey)try{await u.initializeFaceTec(o.data.deviceKey,o.data.encryptionKey),console.log("FaceTec SDK initialized successfully"),o.faceTecInitialized=!0}catch(e){console.error("Error initializing FaceTec SDK:",e),o.faceTecError=e.message||"Failed to initialize FaceTec SDK"}return o}catch(e){throw console.error("Error getting FaceTec session token with eKYC token:",e),e}},m=async(e={},t=null,o=null)=>{try{if(!t)throw new Error("deviceKey parameter is required for Photo ID Scan");if(!o.faceTecInitialized)throw new Error("FaceTec SDK not initialized properly");const n=await u.loadFaceTecSDK();n.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources"),n.setImagesDirectory("/core-sdk/FaceTec_images");const r={onComplete:(e,t,s)=>({sessionResult:e,idScanResult:t,networkResponseStatus:s})},a={"X-Session-Id":e["X-Session-Id"]||o.data?.sessionId,"X-Ekyc-Token":o.data?.ekycToken||l.getEkycToken(),correlationid:e.correlationid||s(411).UuidGenerator.getUniqueId()},i=new S(o.data.sessionFaceTec,r,t,a);return new Promise(((e,t)=>{r.onComplete=(s,o,n)=>{i.isSuccess()?e({sessionResult:s,idScanResult:o,networkResponseStatus:n}):t(new Error("ID scan failed"))}}))}catch(e){throw console.error("Error performing photo ID scan:",e),e}};e.exports={formatCurrency:(e,t="USD")=>g.execute(e,t),greet:e=>h.execute(e),getSessionToken:T,getStoredEkycToken:()=>l.getEkycToken(),clearEkycToken:()=>l.removeEkycToken(),getFaceTecSessionTokenWithEkycToken:I,performPhotoIDScan:m,initEkyc:async(e={})=>{const{sessionId:t,token:o,environment:n="development",language:r="en",initCallback:a}=e;if(!t)throw new Error("sessionId is required for eKYC initialization");if(!o)throw new Error("token is required for eKYC initialization");try{console.log("🚀 Initializing eKYC SDK with sessionId:",t);const{UuidGenerator:e}=s(411);let i=l.getToken("ekyc_device_id");i||(i=e.getUniqueId(),l.storeToken("ekyc_device_id",i)),l.storeToken("ekyc_session_id",t),l.storeToken("ekyc_api_token",o),l.storeToken("ekyc_environment",n),l.storeToken("ekyc_language",r);const c={Authorization:`Bearer ${o}`,"X-Session-Id":t,"X-Ekyc-Device-Info":`browser|${i}|${"undefined"!=typeof window?window.location.origin:"unknown"}|${r}|${r.toUpperCase()}`};console.log("📡 Getting session token...");const d=await T(c,!0);console.log("🎭 Getting FaceTec session token and initializing...");const u=await I(c,!0),p={success:!0,sessionToken:d,faceTecToken:u,environment:n,language:r,sessionId:t,initialized:!0,faceTecInitialized:u.faceTecInitialized||!1};return console.log("✅ eKYC SDK initialized successfully"),a&&"function"==typeof a&&a(p),p}catch(e){console.error("❌ Error initializing eKYC SDK:",e);const s={success:!1,error:e.message||"Failed to initialize eKYC SDK",environment:n,language:r,sessionId:t,initialized:!1};throw a&&"function"==typeof a&&a(s),e}},ocrIdCard:async(e={})=>{const{checkExpiredIdCard:t=!0,checkDopa:s=!1,enableConfirmInfo:o=!0,callback:n}=e;try{console.log("📄 Starting OCR ID Card scan...");const e=l.getToken("ekyc_session_id"),r=l.getToken("ekyc_device_id")||"unknown";if(!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const a={"X-Session-Id":e,"X-Ekyc-Device-Info":`browser|${r}`,"X-Ekyc-Token":l.getToken("ekyc_token")};console.log("🎭 Ensuring FaceTec SDK is initialized...");const i=await I(a,!0);if(!i.faceTecInitialized)throw new Error("FaceTec SDK not properly initialized");console.log("🔍 Performing ID scan...");const c={success:!0,ocrData:await m(a,r,i),checkExpiredIdCard:t,checkDopa:s,enableConfirmInfo:o,sessionId:e,scanType:"id_card_ocr"};return console.log("✅ OCR ID Card scan completed successfully"),n&&"function"==typeof n&&n(c),c}catch(e){console.error("❌ Error performing OCR ID card scan:",e);const r={success:!1,error:e.message||"Failed to perform OCR ID card scan",checkExpiredIdCard:t,checkDopa:s,enableConfirmInfo:o};throw n&&"function"==typeof n&&n(r),e}},ocrIdCardVerifyByFace:async(e={})=>{const{checkExpiredIdCard:t=!0,checkDopa:o=!1,enableConfirmInfo:n=!0,callback:r}=e;try{console.log("📄 Starting OCR ID Card with facial verification scan...");const e=l.getToken("ekyc_session_id"),a=l.getToken("ekyc_device_id")||"unknown";if(!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const i={"X-Session-Id":e,"X-Ekyc-Device-Info":`browser|${a}`,"X-Ekyc-Token":l.getToken("ekyc_token")};console.log("🎭 Ensuring FaceTec SDK is initialized...");const c=await I(i,!0);if(!c.faceTecInitialized)throw new Error("FaceTec SDK not properly initialized");console.log("🔍 Performing ID scan...");const d=await(async(e={},t=null,o=null)=>{try{if(!t)throw new Error("deviceKey parameter is required for Photo ID Scan");if(!o.faceTecInitialized)throw new Error("FaceTec SDK not initialized properly");const n=await u.loadFaceTecSDK();n.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources"),n.setImagesDirectory("/core-sdk/FaceTec_images");const r={onComplete:(e,t,s)=>({sessionResult:e,idScanResult:t,networkResponseStatus:s})},a={"X-Session-Id":e["X-Session-Id"]||o.data?.sessionId,"X-Ekyc-Token":o.data?.ekycToken||l.getEkycToken(),correlationid:e.correlationid||s(411).UuidGenerator.getUniqueId()},i=new k(o.data.sessionFaceTec,r,t,a);return new Promise(((e,t)=>{let s=!1;i._overlayShownAndCancelled=()=>{s=!0},r.onComplete=(o,n,r)=>{i.isSuccess()?e({sessionResult:o,idScanResult:n,networkResponseStatus:r}):s?e({sessionResult:o,idScanResult:n,networkResponseStatus:r,cancelled:!0}):t(new Error("ID scan failed"))}}))}catch(e){throw console.error("Error performing photo ID scan:",e),e}})(i,a,c),p={success:!0,ocrData:d,checkExpiredIdCard:t,checkDopa:o,enableConfirmInfo:n,sessionId:e,scanType:"id_card_ocr"};return console.log("✅ OCR ID Card scan completed successfully"),r&&"function"==typeof r&&r(p),p}catch(e){console.error("❌ Error performing OCR ID card scan:",e);const s={success:!1,error:e.message||"Failed to perform OCR ID card scan",checkExpiredIdCard:t,checkDopa:o,enableConfirmInfo:n};throw r&&"function"==typeof r&&r(s),e}},ndidVerification:async(e={})=>{const{identifierType:t,identifierValue:s,serviceId:o,ndidVerificationCallback:n}=e;if(!t)throw new Error("identifierType is required for NDID verification");if(!s)throw new Error("identifierValue is required for NDID verification");if(!o)throw new Error("serviceId is required for NDID verification");try{console.log("🆔 Starting NDID verification...");const e=l.getToken("ekyc_session_id");if(l.getToken("ekyc_device_id"),!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const r=`ndid_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;await new Promise((e=>setTimeout(e,2e3)));const a={success:!0,ndidVerified:!0,identifierType:t,identifierValue:s,serviceId:o,sessionId:e,verificationId:r,timestamp:(new Date).toISOString(),ndidResponse:{status:"verified",confidence:.95,details:{identityConfirmed:!0,documentValid:!0,biometricMatch:!0}}};return console.log("✅ NDID verification completed successfully"),n&&"function"==typeof n&&n(a),a}catch(e){console.error("❌ Error performing NDID verification:",e);const r={success:!1,error:e.message||"Failed to perform NDID verification",identifierType:t,identifierValue:s,serviceId:o};throw n&&"function"==typeof n&&n(r),e}},livenessCheck:async(e={})=>{const{livenessCheckCallback:t}=e;try{console.log("👁️ Starting liveness check...");const e=l.getToken("ekyc_session_id"),o=l.getToken("ekyc_device_id")||"unknown";if(!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const n={"X-Session-Id":e,"X-Ekyc-Device-Info":`browser|${o}`};console.log("🎭 Ensuring FaceTec SDK is initialized...");const r=await I(n,!0);if(!r.faceTecInitialized)throw new Error("FaceTec SDK not properly initialized");const a={"X-Device-Key":o||r.data?.deviceKey,"X-Session-Id":e,"X-Ekyc-Token":r.data?.ekycToken||l.getEkycToken(),"X-Tid":d.getUniqueId(),correlationid:d.getUniqueId()},i={onComplete:(e,t)=>({sessionResult:e,networkResponseStatus:t})},c=new Promise(((e,t)=>{i.onComplete=(s,o)=>{u.isSuccess()||s&&s.status===FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully?e({sessionResult:s,networkResponseStatus:o}):t(new Error("Liveness check failed or was cancelled"))}}));console.log("🔍 Creating LivenessCheckProcessor...");const u=new(s(266))(r.data.sessionFaceTec,i,a["X-Device-Key"],a);console.log("⏳ Waiting for liveness check to complete...");const p=await c,g={success:u.isSuccess(),liveness:{sessionId:p.sessionResult?.sessionId||`liveness_${Date.now()}`,livenessScore:1,isLive:u.isSuccess(),confidence:u.isSuccess()?"high":"low",timestamp:(new Date).toISOString()},sessionId:e,deviceId:o,faceTecInitialized:!0,rawResult:p};return console.log("✅ Liveness check completed successfully:",g.success),t&&"function"==typeof t&&t({responseCode:g.success?"CUS-KYC-1000":"ERROR",description:g.success?"Liveness check successful":"Unable to Process",success:g.success,data:g}),g}catch(e){throw console.error("❌ Error performing liveness check:",e),e.message,t&&"function"==typeof t&&t({responseCode:"ERROR",responseDescription:e.message||"Failed to perform liveness check",success:!1,error:e.message}),e}}}},382:(e,t,s)=>{const o=new(s(706));e.exports={loadFaceTecSDK:()=>o.loadFaceTecSDK(),initializeFaceTec:(e,t)=>o.initializeFaceTec(e,t),getFaceTecVersion:()=>o.getFaceTecVersion()}},411:(e,t,s)=>{const o=s(599),n=s(641);e.exports={UuidGenerator:o,TokenStorage:n}},453:(e,t,s)=>{const o=s(0),n=s(307),r=s(719),a=s(345);var i=i=function(e,t,s,i){var c=this;this.latestNetworkRequest=new XMLHttpRequest,this.deviceKey=s||null,this.additionalHeaders=i||{},this.postIDScanOnlyUseCase=new o,this.postLivenessCheckUseCase=new n,this.processSessionResultWhileFaceTecSDKWaits=function(e,t){if(c.latestSessionResult=e,e.status!==FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully)return c.latestNetworkRequest.abort(),c.latestNetworkRequest=new XMLHttpRequest,void t.cancel();c.executeLivenessCheckUseCase(e,t)},this.executeLivenessCheckUseCase=async function(e,t){try{const s=function(e){t.uploadProgress(e)},o=await c.postLivenessCheckUseCase.execute({sessionResult:e,deviceKey:c.deviceKey,additionalHeaders:c.additionalHeaders,onProgress:s});o.success?(FaceTecSDK.FaceTecCustomization.setOverrideResultScreenSuccessMessage("Face Scanned\n3D Liveness Proven"),t.proceedToNextStep(o.scanResultBlob)):c.cancelDueToNetworkError(o.errorMessage||"Liveness check failed",t)}catch(e){console.error("Liveness check use case error:",e),c.cancelDueToNetworkError("Network error during liveness check",t)}},this.processIDScanResultWhileFaceTecSDKWaits=function(e,t){if(c.latestIDScanResult=e,e.status!==FaceTecSDK.FaceTecIDScanStatus.Success)return c.latestNetworkRequest.abort(),c.latestNetworkRequest=new XMLHttpRequest,void t.cancel();c.executeIDScanUseCase(e,t)},this.executeIDScanUseCase=async function(e,t){try{const s=function(e){t.uploadProgress(e)},o=await c.postIDScanOnlyUseCase.execute({idScanResult:e,deviceKey:c.deviceKey,additionalHeaders:c.additionalHeaders,onProgress:s});o.success?(e.backImages&&e.backImages[0]&&c.showSuccessOverlay(o.userOcrValue),FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Front Scan Complete","Front of ID<br/>Scanned","ID Scan Complete","Passport Scan Complete","Photo ID Scan<br/>Complete","ID Photo Capture<br/>Complete","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again"),t.proceedToNextStep(o.scanResultBlob)):c.cancelDueToNetworkError(o.errorMessage||"ID scan failed",t)}catch(e){console.error("ID scan use case error:",e),c.cancelDueToNetworkError("Network error during ID scan",t)}},this.showSuccessOverlay=function(e=null){let t=null;e&&(t=a.validateAll(e),r.logData("OCR Validation Result",t));const s=document.createElement("div");s.id="ekyc-success-overlay",s.style.cssText="\n                position: fixed;\n                top: 0;\n                left: 0;\n                width: 100%;\n                height: 100%;\n                background: rgba(0, 0, 0, 0.9);\n                display: flex;\n                justify-content: center;\n                align-items: center;\n                z-index: 10000;\n                font-family: Arial, sans-serif;\n                overflow-y: auto;\n                padding: 20px;\n                box-sizing: border-box;\n            ";const o=e?c.generateOcrFieldsHtml(e,t):"";s.innerHTML=`\n                <div style="\n                    background: white;\n                    border-radius: 12px;\n                    max-width: 600px;\n                    width: 100%;\n                    max-height: 90vh;\n                    overflow-y: auto;\n                    box-shadow: 0 8px 32px rgba(0,0,0,0.3);\n                ">\n                    \x3c!-- Header --\x3e\n                    <div style="\n                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n                        color: white;\n                        padding: 24px;\n                        border-radius: 12px 12px 0 0;\n                        text-align: center;\n                        position: relative;\n                    ">\n                        <h2 style="margin: 0; font-size: 24px; font-weight: 600;">Review & Confirm</h2>\n                        <p style="margin: 8px 0 0 0; opacity: 0.9; font-size: 14px;">Information</p>\n                        <button onclick="document.getElementById('ekyc-success-overlay').remove()"\n                                style="\n                                    position: absolute;\n                                    top: 16px;\n                                    right: 16px;\n                                    background: rgba(255,255,255,0.2);\n                                    border: none;\n                                    color: white;\n                                    width: 32px;\n                                    height: 32px;\n                                    border-radius: 50%;\n                                    cursor: pointer;\n                                    font-size: 18px;\n                                    display: flex;\n                                    align-items: center;\n                                    justify-content: center;\n                                ">×</button>\n                    </div>\n\n                    \x3c!-- Content --\x3e\n                    <div style="padding: 24px;">\n                        ${o||'<div style="text-align: center; color: #666; padding: 40px;"><div style="font-size: 48px; margin-bottom: 16px;">✓</div><h3 style="margin: 0 0 8px 0;">ID Card Scan Complete</h3><p style="margin: 0; opacity: 0.7;">Back side of ID card successfully scanned</p></div>'}\n\n                        \x3c!-- Action Buttons --\x3e\n                        <div style="margin-top: 32px; text-align: center;">\n                            <button onclick="document.getElementById('ekyc-success-overlay').remove()"\n                                    style="\n                                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n                                        color: white;\n                                        border: none;\n                                        padding: 12px 32px;\n                                        border-radius: 8px;\n                                        font-size: 16px;\n                                        font-weight: 600;\n                                        cursor: pointer;\n                                        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n                                        transition: transform 0.2s;\n                                    "\n                                    onmouseover="this.style.transform='translateY(-2px)'"\n                                    onmouseout="this.style.transform='translateY(0)'">\n                                Confirm Information\n                            </button>\n                        </div>\n\n                        ${t&&t.errors.length>0?'\n                        <div style="margin-top: 16px; padding: 12px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px;">\n                            <p style="margin: 0; color: #856404; font-size: 14px; font-weight: 600;">Please check your information before submitting</p>\n                        </div>\n                        ':""}\n                    </div>\n                </div>\n            `,document.body.appendChild(s),c._overlayShownAndCancelled&&c._overlayShownAndCancelled(),faceTecIdScanResultCallback.cancel(),setTimeout((()=>{document.getElementById("ekyc-success-overlay")&&document.getElementById("ekyc-success-overlay").remove()}),3e4)},this.generateOcrFieldsHtml=function(e,t){let s="";return[{key:"nationalId",label:"National ID Number",required:!0},{key:"titleTh",label:"Title (TH)",required:!0},{key:"firstNameTh",label:"First Name (TH)",required:!0},{key:"middleNameTh",label:"Middle Name (TH)",required:!1,note:"(if applicable)"},{key:"lastNameTh",label:"Last Name (TH)",required:!0},{key:"titleEn",label:"Title (ENG)",required:!0},{key:"firstNameEn",label:"First Name (ENG)",required:!0},{key:"middleNameEn",label:"Middle Name (ENG)",required:!1,note:"(if applicable)"},{key:"lastNameEn",label:"Last Name (ENG)",required:!0},{key:"dateOfBirth",label:"Birth Date",required:!0,note:"(A.D. i.e. 2022)",isDate:!0},{key:"dateOfIssue",label:"Issue Date",required:!0,note:"(A.D. i.e. 2022)",isDate:!0},{key:"dateOfExpiry",label:"Expiry Date",required:!0,note:"(A.D. i.e. 2022)",isDate:!0},{key:"laserId",label:"Laser ID",required:!0}].forEach((o=>{const n=e[o.key]||"",r=t?.fieldValidations[o.key],i=r?.status||"unknown",c=a.getStatusIcon(i),l=a.getStatusColor(i);let d=n,u=null;o.isDate&&n&&(u=n.split(/[\/\-\.]/),3===u.length&&(d=`${u[0]} / ${u[1]} / ${u[2]}`)),s+=`\n                    <div style="margin-bottom: 16px;">\n                        <label style="\n                            display: block;\n                            font-size: 14px;\n                            font-weight: 600;\n                            color: #333;\n                            margin-bottom: 6px;\n                        ">\n                            ${o.label}${o.required?"*":""}\n                            ${o.note?`<span style="font-weight: 400; color: #666; font-size: 12px;"> ${o.note}</span>`:""}\n                            <span style="color: ${l}; margin-left: 8px; font-size: 16px;">${c}</span>\n                        </label>\n                        ${o.isDate?`\n                            <div style="display: flex; gap: 8px; align-items: center;">\n                                <input type="text" value="${u&&u[0]?u[0]:""}" placeholder="DD"\n                                       style="width: 60px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; text-align: center;" readonly />\n                                <span style="color: #666;">/</span>\n                                <input type="text" value="${u&&u[1]?u[1]:""}" placeholder="MM"\n                                       style="width: 60px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; text-align: center;" readonly />\n                                <span style="color: #666;">/</span>\n                                <input type="text" value="${u&&u[2]?u[2]:""}" placeholder="YYYY"\n                                       style="width: 80px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; text-align: center;" readonly />\n                            </div>\n                        `:`\n                            <input type="text" value="${d}"\n                                   style="\n                                       width: 100%;\n                                       padding: 12px;\n                                       border: 1px solid ${"invalid"===i||"missing"===i?"#f44336":"#ddd"};\n                                       border-radius: 6px;\n                                       font-size: 14px;\n                                       background: ${"missing"===i?"#fff3e0":"#f9f9f9"};\n                                       color: #333;\n                                   " readonly />\n                        `}\n                        ${r&&!r.isValid&&r.error?`\n                            <div style="margin-top: 4px; color: #f44336; font-size: 12px;">\n                                ${r.error}\n                            </div>\n                        `:""}\n                    </div>\n                `})),s},this.onFaceTecSDKCompletelyDone=function(){null!=c.latestIDScanResult&&(c.success=c.latestIDScanResult.isCompletelyDone),c.sampleAppControllerReference.onComplete(c.latestSessionResult,c.latestIDScanResult,c.latestNetworkRequest.status)},this.cancelDueToNetworkError=function(e,t){!1===c.cancelledDueToNetworkError&&(console.error(e),c.cancelledDueToNetworkError=!0,t.cancel())},this.isSuccess=function(){return c.success},this.success=!1,this.sampleAppControllerReference=t,this.latestSessionResult=null,this.latestIDScanResult=null,this.cancelledDueToNetworkError=!1,FaceTecSDK.FaceTecCustomization.setIDScanUploadMessageOverrides("Uploading<br/>Encrypted<br/>ID Scan","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>ID Scan","Uploading<br/>Encrypted<br/>Back of ID","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>Back of ID","Uploading<br/>Your Confirmed Info","Still Uploading...<br/>Slow Connection","Info Saved","Processing"),new FaceTecSDK.FaceTecSession(this,e)};e.exports=i},518:(e,t,s)=>{const o=s(719);e.exports=class{constructor(){this.baseUrl="/api"}async postIDScanOnly(e,t={},s=null){return new Promise(((n,r)=>{const a=performance.now();try{const i=`${this.baseUrl}/idscan-only`;o.logApiCall(i,"POST","Starting request");const c=new XMLHttpRequest;s&&"function"==typeof s&&(c.upload.onprogress=function(e){if(e.lengthComputable){const t=e.loaded/e.total;o.logIDScanProgress("Uploading",t),s(t)}}),c.onreadystatechange=function(){if(c.readyState===XMLHttpRequest.DONE){const e=performance.now();try{if(c.status>=200&&c.status<300){const t=JSON.parse(c.responseText);o.logPerformance("IDScanDataSource.postIDScanOnly",a,e),o.logApiCall(i,"POST",`Success (${c.status})`),o.logData("API Response",{status:c.status,wasProcessed:t.wasProcessed,error:t.error,hasScanResultBlob:!!t.scanResultBlob}),n(t)}else o.logPerformance("IDScanDataSource.postIDScanOnly (failed)",a,e),o.logError(`API call failed with status ${c.status}`),r(new Error(`HTTP error! status: ${c.status}`))}catch(e){o.logError("Failed to parse API response",e),r(new Error("Failed to parse response JSON"))}}},c.onerror=function(){const e=performance.now();o.logPerformance("IDScanDataSource.postIDScanOnly (network error)",a,e),o.logError("Network request failed"),r(new Error("Network request failed"))},c.open("POST",i),c.setRequestHeader("Content-Type","application/json"),Object.keys(t).forEach((e=>{void 0!==t[e]&&c.setRequestHeader(e,t[e])}));const l=JSON.stringify(e);o.logMessage(`Sending request to ${i} with ${Object.keys(e).length} data fields`),c.send(l)}catch(e){o.logError("IDScanDataSource - postIDScanOnly error",e),r(e)}}))}}},548:(e,t,s)=>{const o=s(599),{TokenStorage:n}=s(411);e.exports=class{async getSessionToken(e={}){try{const t="/api/session-token",s=e["X-Ekyc-Device-Info"]?null:o.getDeviceId(),r=o.getUniqueId(),a=e["X-Session-Id"]||o.getUniqueId();e["X-Session-Id"]||n.storeSessionId(a);const i={"Content-Type":"application/json",Accept:"application/json","X-Ekyc-Sdk-Version":"1.0.0","X-Ekyc-Device-Info":`browser|${s}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`,"X-Session-Id":`${a}`,"X-Tid":`${r}`,correlationid:`${o.getUniqueId()}`,...e};e.Authorization&&(i.Authorization=e.Authorization);const c=await fetch(t,{method:"GET",headers:i});if(!c.ok)throw new Error(`API request failed with status ${c.status}`);return await c.json()}catch(e){throw console.error("Error getting session token:",e),e}}async getFaceTecSessionToken(e={}){try{const t="/api/facetec-session-token",s=e["X-Ekyc-Device-Info"]?null:o.getDeviceId(),r=o.getUniqueId(),a=n.getSessionId(),i={"Content-Type":"application/json",Accept:"application/json","X-Ekyc-Sdk-Version":"1.0.0","X-Ekyc-Device-Info":`browser|${s}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`,"X-Session-Id":`${e["X-Session-Id"]||a||o.getUniqueId()}`,"X-Tid":`${r}`,correlationid:`${o.getUniqueId()}`,...e};e.Authorization&&(i.Authorization=e.Authorization);const c=await fetch(t,{method:"GET",headers:i});if(!c.ok)throw new Error(`API request failed with status ${c.status}`);return await c.json()}catch(e){throw console.error("Error getting FaceTec session token:",e),e}}async getFaceTecSessionTokenWithEkycToken(e={}){try{const t="/api/facetec-session-token",s=n.getEkycToken();if(!s)throw new Error("No eKYC token found. Please get a session token first.");const r=e["X-Ekyc-Device-Info"]?null:o.getDeviceId(),a=e["X-Tid"]||o.getUniqueId(),i=n.getSessionId(),c=e["X-Session-Id"]||i||o.getUniqueId(),l=e.correlationid||o.getUniqueId(),d={"Content-Type":"application/json",Accept:"application/json","X-Ekyc-Sdk-Version":e["X-Ekyc-Sdk-Version"]||"1.0.0","X-Ekyc-Device-Info":e["X-Ekyc-Device-Info"]||`browser|${r}`,"X-Session-Id":`${c}`,"X-Tid":`${a}`,correlationid:`${l}`,"X-Ekyc-Token":s,...e};e.Authorization&&(d.Authorization=e.Authorization);const u=await fetch(t,{method:"GET",headers:d});if(!u.ok)throw new Error(`API request failed with status ${u.status}`);return await u.json()}catch(e){throw console.error("Error getting FaceTec session token with eKYC token:",e),e}}}},592:(e,t,s)=>{const o=s(189);e.exports=class{execute(e,t="USD"){return new o(e,t).format()}}},599:e=>{e.exports=class{static generateUuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}static getDeviceId(){if("undefined"!=typeof window&&window.localStorage){let e=localStorage.getItem("ekyc_device_id");return e||(e=this.generateUuid(),localStorage.setItem("ekyc_device_id",e)),e}return this.generateUuid()}static getUniqueId(){return this.generateUuid()}}},641:e=>{e.exports=class{static storeToken(e,t){if("undefined"!=typeof window&&window.localStorage&&t)try{return localStorage.setItem(e,t),!0}catch(e){return console.error("Error storing token:",e),!1}return!1}static getToken(e){return"undefined"!=typeof window&&window.localStorage?localStorage.getItem(e):null}static removeToken(e){if("undefined"!=typeof window&&window.localStorage)try{return localStorage.removeItem(e),!0}catch(e){return console.error("Error removing token:",e),!1}return!1}static storeEkycToken(e){return this.storeToken("ekyc_token",e)}static getEkycToken(){return this.getToken("ekyc_token")}static removeEkycToken(){return this.removeToken("ekyc_token")}static storeSessionId(e){return this.storeToken("ekyc_session_id",e)}static getSessionId(){return this.getToken("ekyc_session_id")}static removeSessionId(){return this.removeToken("ekyc_session_id")}}},706:e=>{e.exports=class{loadFaceTecSDK(){return new Promise(((e,t)=>{if("undefined"!=typeof window&&window.FaceTecSDK)return void e(window.FaceTecSDK);const s=document.createElement("script");s.src="/core-sdk/FaceTecSDK.js/FaceTecSDK.js",s.async=!0,s.onload=()=>{window.FaceTecSDK?e(window.FaceTecSDK):t(new Error("FaceTecSDK not found after loading script"))},s.onerror=()=>{t(new Error("Failed to load FaceTecSDK script"))},document.head.appendChild(s)}))}async initializeFaceTec(e,t){try{const s=await this.loadFaceTecSDK();return s.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources"),s.setImagesDirectory("/core-sdk/FaceTec_images"),new Promise(((o,n)=>{s.initializeInDevelopmentMode(e,t,(e=>{e?(console.log("FaceTecSDK initialized successfully"),o(!0)):(console.error("FaceTecSDK failed to initialize"),n(new Error("FaceTecSDK failed to initialize")))}))}))}catch(e){throw console.error("Error loading FaceTecSDK:",e),e}}async getFaceTecVersion(){try{return(await this.loadFaceTecSDK()).version()}catch(e){throw console.error("Error getting FaceTecSDK version:",e),e}}}},719:e=>{e.exports=class{static logMessage(e,t="info"){const s=`[${(new Date).toISOString()}] [${t.toUpperCase()}]`;switch(t){case"error":console.error(`${s} ${e}`);break;case"warn":console.warn(`${s} ${e}`);break;case"success":console.log(`%c${s} ${e}`,"color: green; font-weight: bold;");break;default:console.log(`${s} ${e}`)}}static logFaceTecStatus(e){this.logMessage(`FaceTec SDK: ${e}`,"info")}static logApiCall(e,t,s){this.logMessage(`API ${t} ${e}: ${s}`,"info")}static logSuccess(e){this.logMessage(e,"success")}static logError(e,t=null){let s=e;t&&(s+=` - ${t.message}`),this.logMessage(s,"error"),t&&t.stack&&console.error("Stack trace:",t.stack)}static logWarning(e){this.logMessage(e,"warn")}static logIDScanProgress(e,t=null){let s=`ID Scan: ${e}`;null!==t&&(s+=` (${Math.round(100*t)}%)`),this.logMessage(s,"info")}static logLivenessProgress(e,t=null){let s=`Liveness Check: ${e}`;null!==t&&(s+=` (${Math.round(100*t)}%)`),this.logMessage(s,"info")}static logSession(e,t){this.logMessage(`Session ${e}: ${t}`,"info")}static clearConsole(){"function"==typeof console.clear&&console.clear()}static logData(e,t){console.group(`📊 ${e}`),console.log(t),console.groupEnd()}static logPerformance(e,t,s){const o=s-t;this.logMessage(`Performance: ${e} took ${o.toFixed(2)}ms`,"info")}}},777:(e,t,s)=>{const o=s(719);e.exports=class{constructor(){this.baseUrl="/api"}async postLivenessCheck(e,t={},s=null){return new Promise(((n,r)=>{const a=performance.now();try{const i=`${this.baseUrl}/enrollment-3d`;console.log("=== LIVENESS CHECK DATA SOURCE - STARTING REQUEST ==="),console.log("URL:",i),console.log("Input livenessData:",JSON.stringify(e,null,2)),console.log("Input headers:",JSON.stringify(t,null,2)),console.log("Liveness data keys:",Object.keys(e||{})),console.log("Liveness data structure check:",{hasFunction:!!e?.function,hasFaceScan:!!e?.faceScan,hasAuditTrailImage:!!e?.auditTrailImage,hasLowQualityAuditTrailImage:!!e?.lowQualityAuditTrailImage,hasSessionId:!!e?.sessionId}),o.logApiCall(i,"POST","Starting request");const c=new XMLHttpRequest;s&&"function"==typeof s&&(c.upload.onprogress=function(e){if(e.lengthComputable){const t=e.loaded/e.total;o.logLivenessProgress("Uploading",t),s(t)}}),c.onreadystatechange=function(){if(c.readyState===XMLHttpRequest.DONE){const e=performance.now();try{if(console.log("=== LIVENESS CHECK DATA SOURCE - RESPONSE RECEIVED ==="),console.log("Response status:",c.status),console.log("Response text:",c.responseText),c.status>=200&&c.status<300){const t=JSON.parse(c.responseText);console.log("✅ SUCCESS - Parsed response data:",JSON.stringify(t,null,2)),o.logPerformance("LivenessCheckDataSource.postLivenessCheck",a,e),o.logApiCall(i,"POST",`Success (${c.status})`),o.logData("API Response",{status:c.status,wasProcessed:t.wasProcessed,error:t.error,hasScanResultBlob:!!t.scanResultBlob}),n(t)}else console.log("❌ ERROR - HTTP error status:",c.status),console.log("Error response text:",c.responseText),o.logPerformance("LivenessCheckDataSource.postLivenessCheck (failed)",a,e),o.logError(`API call failed with status ${c.status}`),r(new Error(`HTTP error! status: ${c.status}`))}catch(e){o.logError("Error parsing response",e),r(e)}}},c.onerror=function(){o.logError("Network error occurred"),r(new Error("Network error occurred"))},c.open("POST",i,!0),c.setRequestHeader("Content-Type","application/json"),console.log("Setting Content-Type header: application/json");for(const[e,s]of Object.entries(t))null!=s?(c.setRequestHeader(e,s),console.log(`Setting header: ${e} = ${s}`)):console.log(`Skipping header (undefined/null): ${e} = ${s}`);const l=JSON.stringify(e);console.log("=== LIVENESS CHECK DATA SOURCE - SENDING REQUEST ==="),console.log("Request body (stringified):",l),console.log("Request body size (bytes):",l.length),console.log("Request body keys from original data:",Object.keys(e)),o.logData("Request Body Keys",Object.keys(e)),console.log("📤 SENDING XMLHttpRequest..."),c.send(l)}catch(e){o.logError("Error in postLivenessCheck",e),r(e)}}))}}},863:e=>{e.exports=class{execute(e){return`Hello 12, ${e}!`}}},955:(e,t,s)=>{const o=s(0),n=s(719);e.exports=function(e,t,s,r){var a=this;this.latestNetworkRequest=new XMLHttpRequest,this.deviceKey=s||null,this.additionalHeaders=r||{},this.postIDScanOnlyUseCase=new o,this.processIDScanResultWhileFaceTecSDKWaits=function(e,t){if(a.latestIDScanResult=e,e.status!==FaceTecSDK.FaceTecIDScanStatus.Success)return a.latestNetworkRequest.abort(),a.latestNetworkRequest=new XMLHttpRequest,void t.cancel();a.executeIDScanUseCase(e,t)},this.executeIDScanUseCase=async function(e,t){try{const s=function(e){t.uploadProgress(e)},o=await a.postIDScanOnlyUseCase.execute({idScanResult:e,deviceKey:a.deviceKey,additionalHeaders:a.additionalHeaders,onProgress:s});o.success?(FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Front Scan Complete","Front of ID<br/>Scanned","ID Scan Complete","Passport Scan Complete","Photo ID Scan<br/>Complete","ID Photo Capture<br/>Complete","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again"),t.proceedToNextStep(o.scanResultBlob)):a.cancelDueToNetworkError(o.errorMessage||"Unexpected API response, cancelling out.",t)}catch(e){console.error("PhotoIDScanProcessor - executeIDScanUseCase error:",e),a.cancelDueToNetworkError(e.message||"Exception while handling API response, cancelling out.",t)}},this.onFaceTecSDKCompletelyDone=function(){null!==a.latestIDScanResult&&(a.success=a.latestIDScanResult.isCompletelyDone),a.success&&n.logMessage("Id Scan Complete"),a.sampleAppControllerReference.onComplete(null,a.latestIDScanResult,200)},this.cancelDueToNetworkError=function(e,t){!1===a.cancelledDueToNetworkError&&(console.error(e),a.cancelledDueToNetworkError=!0,t.cancel())},this.isSuccess=function(){return a.success},this.success=!1,this.sampleAppControllerReference=t,this.latestIDScanResult=null,this.cancelledDueToNetworkError=!1,FaceTecSDK.FaceTecCustomization.setIDScanUploadMessageOverrides("Uploading<br/>Encrypted<br/>ID Scan","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>ID Scan","Uploading<br/>Encrypted<br/>Back of ID","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>Back of ID","Uploading<br/>Your Confirmed Info","Still Uploading...<br/>Slow Connection","Info Saved","Processing"),new FaceTecSDK.FaceTecSession(this,e)}},985:e=>{e.exports=class{constructor(e){this.authRepository=e}async execute(e={}){return await this.authRepository.getFaceTecSessionTokenWithEkycToken(e)}}},995:e=>{e.exports=class{constructor(e){this.authRepository=e}async execute(e={}){return await this.authRepository.getSessionToken(e)}}}},t={},function s(o){var n=t[o];if(void 0!==n)return n.exports;var r=t[o]={exports:{}};return e[o](r,r.exports,s),r.exports}(347);var e,t}));