!function(e,o){"object"==typeof exports&&"object"==typeof module?module.exports=o():"function"==typeof define&&define.amd?define("api/match-3d-2d-idscan-front",[],o):"object"==typeof exports?exports["api/match-3d-2d-idscan-front"]=o():e["api/match-3d-2d-idscan-front"]=o()}(this,(()=>{return e={137:e=>{async function o(e,o){if("POST"!==e.method)return o.status(405).json({error:"Method not allowed"});try{const s=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/match-3d-2d-idscan/front`;if(console.log("=== FRONT ID SCAN API HANDLER - REQUEST ANALYSIS ==="),console.log("📤 Target URL:",s),console.log("📋 Request body type:",typeof e.body),console.log("📋 Request body constructor:",e.body?.constructor?.name),console.log("📋 Request body keys:",e.body?Object.keys(e.body):"No body"),!e.body)return console.error("❌ Request body is missing"),o.status(400).json({wasProcessed:!1,error:!0,errorMessage:"Request body is required"});const n={hasIdScan:!!e.body.idScan,hasIdScanFrontImage:!!e.body.idScanFrontImage,hasIdScanBackImage:!!e.body.idScanBackImage,hasEnableConfirmInfo:void 0!==e.body.enableConfirmInfo,enableConfirmInfoValue:e.body.enableConfirmInfo};console.log("📊 Front scan body analysis:",n),e.body.idScan||console.error("❌ Missing required parameter: idScan"),e.body.idScanFrontImage||console.error("❌ Missing required parameter: idScanFrontImage");const r={"Content-Type":"application/json",Accept:"application/json",Authorization:e.headers.authorization||(process.env.JWT_TOKEN?`Bearer ${process.env.JWT_TOKEN}`:void 0),"X-Device-Key":e.headers["x-device-key"],"X-User-Agent":e.headers["x-user-agent"],"X-Session-Id":e.headers["x-session-id"],"X-Tid":e.headers["x-tid"],"X-Ekyc-Token":e.headers["x-ekyc-token"],"X-Ekyc-Sdk-Version":e.headers["x-ekyc-sdk-version"]||"1.0.0",correlationid:e.headers.correlationid};Object.keys(r).forEach((e=>{void 0===r[e]&&delete r[e]})),console.log("📋 Request headers:",Object.keys(r));const t=JSON.stringify(e.body);if(console.log("📏 Request body size:",t.length,"characters"),t.length>1e3){const e=t.substring(0,500)+"...[truncated]..."+t.substring(t.length-500);console.log("📄 Request body (truncated):",e)}else console.log("📄 Request body:",t);console.log("📤 Sending request to backend...");const a=await fetch(s,{method:"POST",headers:r,body:t}),c=await a.json();if(console.log("=== FRONT ID SCAN API HANDLER - RESPONSE ANALYSIS ==="),console.log("📥 Response status:",a.status),console.log("📥 Response ok:",a.ok),console.log("📥 Response code:",c.code),console.log("📥 Response keys:",Object.keys(c)),c.data&&(console.log("📊 Response data keys:",Object.keys(c.data)),console.log("📊 Has scanResultBlob:",!!c.data.scanResultBlob),c.data.scanResultBlob&&console.log("📦 ScanResultBlob length:",c.data.scanResultBlob.length)),a.ok&&"CUS-KYC-1000"===c.code){const e={wasProcessed:!0,error:!1,scanResultBlob:c.data?.scanResultBlob||"",originalResponse:c};return console.log("✅ SUCCESS - Returning front scan success response"),console.log("📦 ScanResultBlob length:",e.scanResultBlob.length),console.log("=== FRONT ID SCAN API HANDLER - SUCCESS ==="),o.status(200).json(e)}{const e={wasProcessed:!1,error:!0,errorMessage:c.description||c.message||"Unknown error occurred during front ID scan",originalResponse:c};return console.error("❌ ERROR - Front scan failed"),console.error("❌ Error message:",e.errorMessage),console.error("❌ Full response data:",JSON.stringify(c,null,2)),console.error("=== FRONT ID SCAN API HANDLER - ERROR ==="),o.status(a.status).json(e)}}catch(e){return console.error("=== FRONT ID SCAN API HANDLER - EXCEPTION ==="),console.error("❌ Exception type:",e.constructor.name),console.error("❌ Exception message:",e.message),console.error("❌ Exception stack:",e.stack),e.cause&&console.error("❌ Error cause:",e.cause),console.error("=== END FRONT ID SCAN EXCEPTION ==="),o.status(500).json({wasProcessed:!1,error:!0,errorMessage:"Failed to process front ID scan: "+e.message})}}e.exports=o,e.exports.default=o}},o={},function s(n){var r=o[n];if(void 0!==r)return r.exports;var t=o[n]={exports:{}};return e[n](t,t.exports,s),t.exports}(137);var e,o}));