//
// Welcome to the annotated FaceTec Device SDK core code for performing secure Photo ID Scan.
//
//
// This is an example self-contained class to perform Photo ID Scans with the FaceTec SDK.
// You may choose to further componentize parts of this in your own Apps based on your specific requirements.
//

const PostIDScanOnlyUseCase = require('../domain/usecases/PostIDScanOnlyUseCase');
const PostIDScanFrontUseCase = require('../domain/usecases/PostIDScanFrontUseCase');
const PostIDScanBackUseCase = require('../domain/usecases/PostIDScanBackUseCase');
const PostLivenessCheckUseCase = require('../domain/usecases/PostLivenessCheckUseCase');
const DeveloperStatusMessages = require('../infrastructure/utils/DeveloperStatusMessages');
const OcrDataValidator = require('../infrastructure/utils/OcrDataValidator');

var PhotoIDMatchProcessor = /** @class */ (function () {
    function PhotoIDMatchProcessor(sessionToken, sampleAppControllerReference, deviceKey, additionalHeaders, onComplete) {
        var _this = this;
        this.latestNetworkRequest = new XMLHttpRequest();
        this.deviceKey = deviceKey || null; // Store the device key
        this.additionalHeaders = additionalHeaders || {}; // get additional headers
        this.onComplete = onComplete || null; // Store the completion callback
        this.postIDScanOnlyUseCase = new PostIDScanOnlyUseCase(); // Initialize use case
        this.postIDScanFrontUseCase = new PostIDScanFrontUseCase(); // Initialize front scan use case
        this.postIDScanBackUseCase = new PostIDScanBackUseCase(); // Initialize back scan use case
        this.postLivenessCheckUseCase = new PostLivenessCheckUseCase(); // Initialize liveness check use case
        
        //
        // Part 2:  Handling the Result of a FaceScan - First part of the Photo ID Scan
        //
        this.processSessionResultWhileFaceTecSDKWaits = function (sessionResult, faceScanResultCallback) {
            _this.latestSessionResult = sessionResult;
            //
            // Part 3:  Handles early exit scenarios where there is no FaceScan to handle -- i.e. User Cancellation, Timeouts, etc.
            //
            if (sessionResult.status !== FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully) {
                _this.latestNetworkRequest.abort();
                _this.latestNetworkRequest = new XMLHttpRequest();
                faceScanResultCallback.cancel();
                return;
            }
            // IMPORTANT:  FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully DOES NOT mean the Liveness Check was Successful.
            // It simply means the User completed the Session and a 3D FaceScan was created.  You still need to perform the Liveness Check on your Servers.

            //
            // Part 4: Use Clean Architecture - Call UseCase instead of direct API
            //
            _this.executeLivenessCheckUseCase(sessionResult, faceScanResultCallback);
        };

        //
        // New method: Execute Liveness Check using Clean Architecture UseCase
        //
        this.executeLivenessCheckUseCase = async function(sessionResult, faceScanResultCallback) {
            try {
                // Create progress callback for upload tracking
                const onProgress = function(progress) {
                    faceScanResultCallback.uploadProgress(progress);
                };

                // Execute the use case
                const result = await _this.postLivenessCheckUseCase.execute({
                    sessionResult: sessionResult,
                    deviceKey: _this.deviceKey,
                    additionalHeaders: _this.additionalHeaders,
                    onProgress: onProgress
                });

                // Handle the result
                if (result.success) {
                    // Demonstrates dynamically setting the Success Screen Message.
                    FaceTecSDK.FaceTecCustomization.setOverrideResultScreenSuccessMessage("Face Scanned\n3D Liveness Proven");
                    // In v9.2.0+, simply pass in scanResultBlob to the proceedToNextStep function to advance the User flow.
                    // scanResultBlob is a proprietary, encrypted blob that controls the logic for what happens next for the User.
                    faceScanResultCallback.proceedToNextStep(result.scanResultBlob);
                } else {
                    // Handle error case
                    _this.cancelDueToNetworkError(result.errorMessage || "Liveness check failed", faceScanResultCallback);
                }
            } catch (error) {
                console.error("Liveness check use case error:", error);
                _this.cancelDueToNetworkError("Network error during liveness check", faceScanResultCallback);
            }
        };
        //
        // Part 10:  Handling the Result of a IDScan
        //
        this.processIDScanResultWhileFaceTecSDKWaits = function (idScanResult, idScanResultCallback) {
            _this.latestIDScanResult = idScanResult;
            //
            // Part 11:  Handles early exit scenarios where there is no IDScan to handle -- i.e. User Cancellation, Timeouts, etc.
            //
            if (idScanResult.status !== FaceTecSDK.FaceTecIDScanStatus.Success) {
                _this.latestNetworkRequest.abort();
                _this.latestNetworkRequest = new XMLHttpRequest();
                idScanResultCallback.cancel();
                return;
            }
            // IMPORTANT:  FaceTecSDK.FaceTecIDScanStatus.Success DOES NOT mean the IDScan was Successful.
            // It simply means the User completed the Session and a 3D IDScan was created.  You still need to perform the ID-Check on your Servers.

            //
            // Part 12: Use Clean Architecture - Call UseCase instead of direct API
            //
            _this.executeIDScanUseCase(idScanResult, idScanResultCallback);
        };

        //
        // New method: Execute ID Scan using Clean Architecture UseCase with endpoint routing
        //
        this.executeIDScanUseCase = async function(idScanResult, idScanResultCallback) {
            try {
                // Create progress callback for upload tracking
                const onProgress = function(progress) {
                    idScanResultCallback.uploadProgress(progress);
                };

                // Determine scan type and route to appropriate endpoint
                let result;
                let scanType = 'unknown';

                if (idScanResult.backImages && idScanResult.backImages[0]) {
                    // This is a back ID card scan - use back endpoint
                    scanType = 'back';
                    DeveloperStatusMessages.logMessage('Routing to back ID scan endpoint');
                    result = await _this.postIDScanBackUseCase.execute({
                        idScanResult: idScanResult,
                        deviceKey: _this.deviceKey,
                        additionalHeaders: _this.additionalHeaders,
                        onProgress: onProgress
                    });
                } else if (idScanResult.frontImages && idScanResult.frontImages[0]) {
                    // This is a front ID card scan - use front endpoint
                    scanType = 'front';
                    DeveloperStatusMessages.logMessage('Routing to front ID scan endpoint');
                    result = await _this.postIDScanFrontUseCase.execute({
                        idScanResult: idScanResult,
                        deviceKey: _this.deviceKey,
                        additionalHeaders: _this.additionalHeaders,
                        onProgress: onProgress
                    });
                } else {
                    // Fallback to original endpoint for backward compatibility
                    scanType = 'legacy';
                    DeveloperStatusMessages.logMessage('Routing to legacy ID scan endpoint');
                    result = await _this.postIDScanOnlyUseCase.execute({
                        idScanResult: idScanResult,
                        deviceKey: _this.deviceKey,
                        additionalHeaders: _this.additionalHeaders,
                        onProgress: onProgress
                    });
                }

                DeveloperStatusMessages.logData('ID scan completed', { scanType: scanType, success: result.success });

                // Handle the result
                if (result.success) {
                    // Check if this is a retry case (CUS-KYC-7102)
                    if (result.allowRetry) {
                        DeveloperStatusMessages.logMessage(`${scanType} scan: Retry allowed - ${result.retryReason}`);

                        // Configure retry-specific messages
                        _this.configureRetryMessages(scanType);

                        // For retry cases, proceed with scanResultBlob to allow retry
                        idScanResultCallback.proceedToNextStep(result.scanResultBlob);
                    } else {
                        // Normal success case
                        // Check if this is a back ID card scan and show success overlay
                        if (scanType === 'back' && result.userOcrValue) {
                            // This is a back ID card scan - show success overlay with OCR data
                            _this.showSuccessOverlay(result.userOcrValue);
                        }

                        // Configure success messages based on scan type
                        _this.configureSuccessMessages(scanType);

                        // In v9.2.0+, simply pass in scanResultBlob to the proceedToNextStep function to advance the User flow.
                        idScanResultCallback.proceedToNextStep(result.scanResultBlob);
                    }
                } else {
                    // Handle error case - execute callback before cancelling (except for retry cases)
                    if (_this.onComplete && !result.allowRetry) {
                        // Extract OCR data for callback even in error cases
                        const extractedOcrValue = _this.extractOcrValueForCallback(result);

                        // Execute callback with error information
                        _this.onComplete(
                            false, // success = false for errors
                            result.errorMessage || `${scanType} ID scan failed`,
                            extractedOcrValue, // userOcrValue (original OCR data if available)
                            null, // userConfirmedValue (null for errors)
                            null  // dopaResult (reserved for future use)
                        );
                    }

                    _this.cancelDueToNetworkError(result.errorMessage || `${scanType} ID scan failed`, idScanResultCallback);
                }
            } catch (error) {
                console.error("ID scan use case error:", error);

                // Execute callback for network/exception errors
                if (_this.onComplete) {
                    _this.onComplete(
                        false, // success = false for exceptions
                        "Network error during ID scan: " + error.message,
                        null, // userOcrValue (null for exceptions)
                        null, // userConfirmedValue (null for exceptions)
                        null  // dopaResult (reserved for future use)
                    );
                }

                _this.cancelDueToNetworkError("Network error during ID scan", idScanResultCallback);
            }
        };

        //
        // Helper method to configure success messages based on scan type
        //
        this.configureSuccessMessages = function(scanType) {
            if (scanType === 'front') {
                FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides(
                    "Front Scan Complete", // Successful scan of ID front-side (ID Types with no back-side).
                    "Front of ID<br/>Scanned", // Successful scan of ID front-side (ID Types that do have a back-side).
                    "Front Scan Complete", // Successful scan of the ID front-side.
                    "Passport Scan Complete", // Successful scan of a Passport
                    "Photo ID Front<br/>Complete", // Successful upload of final Photo ID Front result.
                    "ID Photo Capture<br/>Complete", // Successful upload of final Photo ID Scan result but scan is flagged as requiring additional review.
                    "Face Didn't Match<br/>Highly Enough", // Case where a Retry is needed because the Face on the Photo ID did not Match the User's Face highly enough.
                    "ID Document<br/>Not Fully Visible", // Case where a Retry is needed because a Full ID was not detected with high enough confidence.
                    "ID Text Not Legible", // Case where a Retry is needed because the OCR did not produce good enough results and the User should Retry with a better capture.
                    "ID Type Mismatch<br/>Please Try Again" // Case where there is likely no OCR Template installed for the document the User is attempting to scan.
                );
            } else if (scanType === 'back') {
                FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides(
                    "Back Scan Complete", // Successful scan of ID back-side.
                    "Back of ID<br/>Scanned", // Successful scan of ID back-side.
                    "ID Scan Complete", // Successful scan of the ID back-side.
                    "Passport Scan Complete", // Successful scan of a Passport
                    "Photo ID Back<br/>Complete", // Successful upload of final Photo ID Back result containing OCR data.
                    "ID Photo Capture<br/>Complete", // Successful upload of final Photo ID Scan result but scan is flagged as requiring additional review.
                    "Face Didn't Match<br/>Highly Enough", // Case where a Retry is needed because the Face on the Photo ID did not Match the User's Face highly enough.
                    "ID Document<br/>Not Fully Visible", // Case where a Retry is needed because a Full ID was not detected with high enough confidence.
                    "ID Text Not Legible", // Case where a Retry is needed because the OCR did not produce good enough results and the User should Retry with a better capture.
                    "ID Type Mismatch<br/>Please Try Again" // Case where there is likely no OCR Template installed for the document the User is attempting to scan.
                );
            } else {
                // Default/legacy messages
                FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides(
                    "Front Scan Complete", // Successful scan of ID front-side (ID Types with no back-side).
                    "Front of ID<br/>Scanned", // Successful scan of ID front-side (ID Types that do have a back-side).
                    "ID Scan Complete", // Successful scan of the ID back-side.
                    "Passport Scan Complete", // Successful scan of a Passport
                    "Photo ID Scan<br/>Complete", // Successful upload of final Photo ID Scan result containing User-Confirmed ID Text.
                    "ID Photo Capture<br/>Complete", // Successful upload of final Photo ID Scan result but scan is flagged as requiring additional review.
                    "Face Didn't Match<br/>Highly Enough", // Case where a Retry is needed because the Face on the Photo ID did not Match the User's Face highly enough.
                    "ID Document<br/>Not Fully Visible", // Case where a Retry is needed because a Full ID was not detected with high enough confidence.
                    "ID Text Not Legible", // Case where a Retry is needed because the OCR did not produce good enough results and the User should Retry with a better capture.
                    "ID Type Mismatch<br/>Please Try Again" // Case where there is likely no OCR Template installed for the document the User is attempting to scan.
                );
            }
        };

        //
        // Helper method to configure retry messages for CUS-KYC-7102 cases
        //
        this.configureRetryMessages = function(scanType) {
            DeveloperStatusMessages.logMessage(`Configuring retry messages for ${scanType} scan (CUS-KYC-7102)`);

            if (scanType === 'front') {
                FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides(
                    "Front Scan Complete<br/>Please Try Again", // Successful scan of ID front-side (ID Types with no back-side).
                    "Front of ID Scanned<br/>Please Try Again", // Successful scan of ID front-side (ID Types that do have a back-side).
                    "Front Scan Complete<br/>Please Try Again", // Successful scan of the ID front-side.
                    "Passport Scan Complete<br/>Please Try Again", // Successful scan of a Passport
                    "Photo ID Front Complete<br/>Please Try Again", // Successful upload of final Photo ID Front result.
                    "ID Photo Capture Complete<br/>Please Try Again", // Successful upload of final Photo ID Scan result but scan is flagged as requiring additional review.
                    "Face Didn't Match<br/>Highly Enough", // Case where a Retry is needed because the Face on the Photo ID did not Match the User's Face highly enough.
                    "ID Document<br/>Not Fully Visible", // Case where a Retry is needed because a Full ID was not detected with high enough confidence.
                    "ID Text Not Legible", // Case where a Retry is needed because the OCR did not produce good enough results and the User should Retry with a better capture.
                    "ID Type Mismatch<br/>Please Try Again" // Case where there is likely no OCR Template installed for the document the User is attempting to scan.
                );
            } else if (scanType === 'back') {
                FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides(
                    "Back Scan Complete<br/>Please Try Again", // Successful scan of ID back-side.
                    "Back of ID Scanned<br/>Please Try Again", // Successful scan of ID back-side.
                    "ID Scan Complete<br/>Please Try Again", // Successful scan of the ID back-side.
                    "Passport Scan Complete<br/>Please Try Again", // Successful scan of a Passport
                    "Photo ID Back Complete<br/>Please Try Again", // Successful upload of final Photo ID Back result containing OCR data.
                    "ID Photo Capture Complete<br/>Please Try Again", // Successful upload of final Photo ID Scan result but scan is flagged as requiring additional review.
                    "Face Didn't Match<br/>Highly Enough", // Case where a Retry is needed because the Face on the Photo ID did not Match the User's Face highly enough.
                    "ID Document<br/>Not Fully Visible", // Case where a Retry is needed because a Full ID was not detected with high enough confidence.
                    "ID Text Not Legible", // Case where a Retry is needed because the OCR did not produce good enough results and the User should Retry with a better capture.
                    "ID Type Mismatch<br/>Please Try Again" // Case where there is likely no OCR Template installed for the document the User is attempting to scan.
                );
            } else {
                // Default/legacy retry messages
                FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides(
                    "Scan Complete<br/>Please Try Again", // Successful scan of ID front-side (ID Types with no back-side).
                    "ID Scanned<br/>Please Try Again", // Successful scan of ID front-side (ID Types that do have a back-side).
                    "ID Scan Complete<br/>Please Try Again", // Successful scan of the ID back-side.
                    "Passport Scan Complete<br/>Please Try Again", // Successful scan of a Passport
                    "Photo ID Scan Complete<br/>Please Try Again", // Successful upload of final Photo ID Scan result containing User-Confirmed ID Text.
                    "ID Photo Capture Complete<br/>Please Try Again", // Successful upload of final Photo ID Scan result but scan is flagged as requiring additional review.
                    "Face Didn't Match<br/>Highly Enough", // Case where a Retry is needed because the Face on the Photo ID did not Match the User's Face highly enough.
                    "ID Document<br/>Not Fully Visible", // Case where a Retry is needed because a Full ID was not detected with high enough confidence.
                    "ID Text Not Legible", // Case where a Retry is needed because the OCR did not produce good enough results and the User should Retry with a better capture.
                    "ID Type Mismatch<br/>Please Try Again" // Case where there is likely no OCR Template installed for the document the User is attempting to scan.
                );
            }
        };

        //
        // Helper method to extract OCR value for callback
        //
        this.extractOcrValueForCallback = function(result) {
            if (!result || !result.userOcrValue) {
                return null;
            }

            // Create UserConfirmedValue structure from OCR data
            const ocrValue = result.userOcrValue;
            return {
                nationalId: ocrValue.nationalId || null,
                titleTh: ocrValue.titleTh || null,
                firstNameTh: ocrValue.firstNameTh || null,
                middleNameTh: ocrValue.middleNameTh || null,
                lastNameTh: ocrValue.lastNameTh || null,
                titleEn: ocrValue.titleEn || null,
                firstNameEn: ocrValue.firstNameEn || null,
                middleNameEn: ocrValue.middleNameEn || null,
                lastNameEn: ocrValue.lastNameEn || null,
                dateOfBirth: ocrValue.dateOfBirth || null,
                dateOfIssue: ocrValue.dateOfIssue || null,
                dateOfExpiry: ocrValue.dateOfExpiry || null,
                laserId: ocrValue.laserId || null
            };
        };

        //
        // Method to show success overlay for back ID card scan with OCR data
        //
        this.showSuccessOverlay = function(userOcrValue) {
            DeveloperStatusMessages.logMessage('Showing success overlay with callback functionality');
            DeveloperStatusMessages.logData('OCR Data for overlay', userOcrValue);

            // Create overlay container
            const overlay = document.createElement('div');
            overlay.id = 'facetec-success-overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                font-family: Arial, sans-serif;
            `;

            // Create content container
            const content = document.createElement('div');
            content.style.cssText = `
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                max-width: 500px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            `;

            // Create title
            const title = document.createElement('h2');
            title.textContent = 'ID Card Information';
            title.style.cssText = `
                margin: 0 0 20px 0;
                color: #333;
                text-align: center;
                font-size: 24px;
            `;

            // Create form for OCR data
            const form = document.createElement('div');
            form.style.cssText = `
                display: grid;
                gap: 15px;
            `;

            // Helper function to create field row
            const createFieldRow = (label, value, fieldName, isEditable = true) => {
                const row = document.createElement('div');
                row.style.cssText = `
                    display: flex;
                    flex-direction: column;
                    gap: 5px;
                `;

                const labelEl = document.createElement('label');
                labelEl.textContent = label;
                labelEl.style.cssText = `
                    font-weight: bold;
                    color: #555;
                    font-size: 14px;
                `;

                const input = document.createElement('input');
                input.type = 'text';
                input.value = value || '';
                input.readOnly = !isEditable;
                input.dataset.fieldName = fieldName;
                input.style.cssText = `
                    padding: 10px;
                    border: 1px solid ${isEditable ? '#ddd' : '#eee'};
                    border-radius: 5px;
                    font-size: 16px;
                    background-color: ${isEditable ? 'white' : '#f9f9f9'};
                    color: ${isEditable ? '#333' : '#666'};
                `;

                // Add date validation for date fields
                if (isEditable && (fieldName.includes('date') || fieldName.includes('Date'))) {
                    input.placeholder = 'dd/MM/yyyy';
                    input.addEventListener('blur', function() {
                        const datePattern = /^\d{2}\/\d{2}\/\d{4}$/;
                        if (this.value && !datePattern.test(this.value)) {
                            this.style.borderColor = '#f44336';
                            this.title = 'Please enter date in dd/MM/yyyy format';
                        } else {
                            this.style.borderColor = '#ddd';
                            this.title = '';
                        }
                    });
                }

                row.appendChild(labelEl);
                row.appendChild(input);
                return { row, input };
            };

            // Determine field editability using metadata if available
            const getFieldEditability = (fieldName) => {
                if (userOcrValue._fieldMetadata && userOcrValue._fieldMetadata.editableFields) {
                    return userOcrValue._fieldMetadata.editableFields.includes(fieldName);
                }
                // Default editability rules
                const readOnlyFields = ['nationalId', 'laserId'];
                return !readOnlyFields.includes(fieldName);
            };

            // Add fields based on OCR data
            const fields = {};

            if (userOcrValue.nationalId !== null && userOcrValue.nationalId !== undefined) {
                const field = createFieldRow('National ID', userOcrValue.nationalId, 'nationalId', getFieldEditability('nationalId'));
                form.appendChild(field.row);
                fields.nationalId = field.input;
            }

            if (userOcrValue.titleTh !== null && userOcrValue.titleTh !== undefined) {
                const field = createFieldRow('Title (Thai)', userOcrValue.titleTh, 'titleTh', getFieldEditability('titleTh'));
                form.appendChild(field.row);
                fields.titleTh = field.input;
            }

            if (userOcrValue.firstNameTh !== null && userOcrValue.firstNameTh !== undefined) {
                const field = createFieldRow('First Name (Thai)', userOcrValue.firstNameTh, 'firstNameTh', getFieldEditability('firstNameTh'));
                form.appendChild(field.row);
                fields.firstNameTh = field.input;
            }

            if (userOcrValue.lastNameTh !== null && userOcrValue.lastNameTh !== undefined) {
                const field = createFieldRow('Last Name (Thai)', userOcrValue.lastNameTh, 'lastNameTh', getFieldEditability('lastNameTh'));
                form.appendChild(field.row);
                fields.lastNameTh = field.input;
            }

            if (userOcrValue.titleEn !== null && userOcrValue.titleEn !== undefined) {
                const field = createFieldRow('Title (English)', userOcrValue.titleEn, 'titleEn', getFieldEditability('titleEn'));
                form.appendChild(field.row);
                fields.titleEn = field.input;
            }

            if (userOcrValue.firstNameEn !== null && userOcrValue.firstNameEn !== undefined) {
                const field = createFieldRow('First Name (English)', userOcrValue.firstNameEn, 'firstNameEn', getFieldEditability('firstNameEn'));
                form.appendChild(field.row);
                fields.firstNameEn = field.input;
            }

            if (userOcrValue.lastNameEn !== null && userOcrValue.lastNameEn !== undefined) {
                const field = createFieldRow('Last Name (English)', userOcrValue.lastNameEn, 'lastNameEn', getFieldEditability('lastNameEn'));
                form.appendChild(field.row);
                fields.lastNameEn = field.input;
            }

            if (userOcrValue.dateOfBirth !== null && userOcrValue.dateOfBirth !== undefined) {
                const field = createFieldRow('Date of Birth (dd/MM/yyyy)', userOcrValue.dateOfBirth, 'dateOfBirth', getFieldEditability('dateOfBirth'));
                form.appendChild(field.row);
                fields.dateOfBirth = field.input;
            }

            if (userOcrValue.dateOfIssue !== null && userOcrValue.dateOfIssue !== undefined) {
                const field = createFieldRow('Date of Issue (dd/MM/yyyy)', userOcrValue.dateOfIssue, 'dateOfIssue', getFieldEditability('dateOfIssue'));
                form.appendChild(field.row);
                fields.dateOfIssue = field.input;
            }

            if (userOcrValue.dateOfExpiry !== null && userOcrValue.dateOfExpiry !== undefined) {
                const field = createFieldRow('Date of Expiry (dd/MM/yyyy)', userOcrValue.dateOfExpiry, 'dateOfExpiry', getFieldEditability('dateOfExpiry'));
                form.appendChild(field.row);
                fields.dateOfExpiry = field.input;
            }

            if (userOcrValue.laserId !== null && userOcrValue.laserId !== undefined) {
                const field = createFieldRow('Laser ID', userOcrValue.laserId, 'laserId', getFieldEditability('laserId'));
                form.appendChild(field.row);
                fields.laserId = field.input;
            }

            // Create button container
            const buttonContainer = document.createElement('div');
            buttonContainer.style.cssText = `
                display: flex;
                gap: 15px;
                margin-top: 25px;
                justify-content: center;
            `;

            // Create confirm button
            const confirmButton = document.createElement('button');
            confirmButton.textContent = 'Confirm Information';
            confirmButton.style.cssText = `
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                font-weight: bold;
                transition: background-color 0.3s;
            `;

            confirmButton.onmouseover = function() {
                this.style.backgroundColor = '#45a049';
            };

            confirmButton.onmouseout = function() {
                this.style.backgroundColor = '#4CAF50';
            };

            confirmButton.onclick = function() {
                // Validate date fields before confirming
                let isValid = true;
                const dateFields = ['dateOfBirth', 'dateOfIssue', 'dateOfExpiry'];
                const datePattern = /^\d{2}\/\d{2}\/\d{4}$/;

                dateFields.forEach(fieldName => {
                    if (fields[fieldName] && !fields[fieldName].readOnly && fields[fieldName].value) {
                        if (!datePattern.test(fields[fieldName].value)) {
                            fields[fieldName].style.borderColor = '#f44336';
                            fields[fieldName].focus();
                            isValid = false;
                        }
                    }
                });

                if (!isValid) {
                    alert('Please enter all dates in dd/MM/yyyy format');
                    return;
                }

                // Collect user-edited values
                const userConfirmedValue = {
                    nationalId: fields.nationalId ? fields.nationalId.value || null : null,
                    titleTh: fields.titleTh ? fields.titleTh.value || null : null,
                    firstNameTh: fields.firstNameTh ? fields.firstNameTh.value || null : null,
                    middleNameTh: userOcrValue.middleNameTh || null, // Preserve original value
                    lastNameTh: fields.lastNameTh ? fields.lastNameTh.value || null : null,
                    titleEn: fields.titleEn ? fields.titleEn.value || null : null,
                    firstNameEn: fields.firstNameEn ? fields.firstNameEn.value || null : null,
                    middleNameEn: userOcrValue.middleNameEn || null, // Preserve original value
                    lastNameEn: fields.lastNameEn ? fields.lastNameEn.value || null : null,
                    dateOfBirth: fields.dateOfBirth ? fields.dateOfBirth.value || null : null,
                    dateOfIssue: fields.dateOfIssue ? fields.dateOfIssue.value || null : null,
                    dateOfExpiry: fields.dateOfExpiry ? fields.dateOfExpiry.value || null : null,
                    laserId: fields.laserId ? fields.laserId.value || null : null
                };

                // Execute callback with user confirmation
                if (_this.onComplete) {
                    const originalOcrValue = _this.extractOcrValueForCallback({ userOcrValue: userOcrValue });

                    DeveloperStatusMessages.logMessage('Executing onComplete callback for user confirmation');
                    DeveloperStatusMessages.logData('Original OCR Value', originalOcrValue);
                    DeveloperStatusMessages.logData('User Confirmed Value', userConfirmedValue);

                    _this.onComplete(
                        true, // success = true for user confirmation
                        "User confirmed information",
                        originalOcrValue, // userOcrValue (original OCR data)
                        userConfirmedValue, // userConfirmedValue (user-edited data)
                        null  // dopaResult (reserved for future use)
                    );
                }

                // Remove overlay
                document.body.removeChild(overlay);
            };

            // Create cancel button
            const cancelButton = document.createElement('button');
            cancelButton.textContent = 'Cancel';
            cancelButton.style.cssText = `
                background-color: #f44336;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                font-weight: bold;
                transition: background-color 0.3s;
            `;

            cancelButton.onmouseover = function() {
                this.style.backgroundColor = '#da190b';
            };

            cancelButton.onmouseout = function() {
                this.style.backgroundColor = '#f44336';
            };

            cancelButton.onclick = function() {
                // Remove overlay without executing callback
                DeveloperStatusMessages.logMessage('User cancelled information confirmation');
                document.body.removeChild(overlay);
            };

            // Assemble the overlay
            buttonContainer.appendChild(confirmButton);
            buttonContainer.appendChild(cancelButton);
            content.appendChild(title);
            content.appendChild(form);
            content.appendChild(buttonContainer);
            overlay.appendChild(content);

            // Add to document
            document.body.appendChild(overlay);

            // Focus on first editable field
            const firstEditableField = Object.values(fields).find(field => !field.readOnly);
            if (firstEditableField) {
                setTimeout(() => firstEditableField.focus(), 100);
            }

            DeveloperStatusMessages.logMessage('Success overlay displayed with editable fields');
        };

        //
        // Method to generate OCR fields HTML with validation indicators
        //
        this.generateOcrFieldsHtml = function(ocrData, validationResult) {
            const fields = [
                { key: 'nationalId', label: 'National ID Number', required: true },
                { key: 'titleTh', label: 'Title (TH)', required: true },
                { key: 'firstNameTh', label: 'First Name (TH)', required: true },
                { key: 'middleNameTh', label: 'Middle Name (TH)', required: false, note: '(if applicable)' },
                { key: 'lastNameTh', label: 'Last Name (TH)', required: true },
                { key: 'titleEn', label: 'Title (ENG)', required: true },
                { key: 'firstNameEn', label: 'First Name (ENG)', required: true },
                { key: 'middleNameEn', label: 'Middle Name (ENG)', required: false, note: '(if applicable)' },
                { key: 'lastNameEn', label: 'Last Name (ENG)', required: true },
                { key: 'dateOfBirth', label: 'Birth Date', required: true, note: '(A.D. i.e. 2022)', isDate: true },
                { key: 'dateOfIssue', label: 'Issue Date', required: true, note: '(A.D. i.e. 2022)', isDate: true },
                { key: 'dateOfExpiry', label: 'Expiry Date', required: true, note: '(A.D. i.e. 2022)', isDate: true },
                { key: 'laserId', label: 'Laser ID', required: true }
            ];

            let html = '';

            fields.forEach(field => {
                const value = ocrData[field.key] || '';
                const validation = validationResult?.fieldValidations[field.key];
                const status = validation?.status || 'unknown';
                const icon = OcrDataValidator.getStatusIcon(status);
                const color = OcrDataValidator.getStatusColor(status);

                // Format date fields
                let displayValue = value;
                let parts = null;
                if (field.isDate && value) {
                    parts = value.split(/[\/\-\.]/);
                    if (parts.length === 3) {
                        displayValue = `${parts[0]} / ${parts[1]} / ${parts[2]}`;
                    }
                }

                html += `
                    <div style="margin-bottom: 16px;">
                        <label style="
                            display: block;
                            font-size: 14px;
                            font-weight: 600;
                            color: #333;
                            margin-bottom: 6px;
                        ">
                            ${field.label}${field.required ? '*' : ''}
                            ${field.note ? `<span style="font-weight: 400; color: #666; font-size: 12px;"> ${field.note}</span>` : ''}
                            <span style="color: ${color}; margin-left: 8px; font-size: 16px;">${icon}</span>
                        </label>
                        ${field.isDate ? `
                            <div style="display: flex; gap: 8px; align-items: center;">
                                <input type="text" value="${parts && parts[0] ? parts[0] : ''}" placeholder="DD"
                                       style="width: 60px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; text-align: center;" readonly />
                                <span style="color: #666;">/</span>
                                <input type="text" value="${parts && parts[1] ? parts[1] : ''}" placeholder="MM"
                                       style="width: 60px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; text-align: center;" readonly />
                                <span style="color: #666;">/</span>
                                <input type="text" value="${parts && parts[2] ? parts[2] : ''}" placeholder="YYYY"
                                       style="width: 80px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; text-align: center;" readonly />
                            </div>
                        ` : `
                            <input type="text" value="${displayValue}"
                                   style="
                                       width: 100%;
                                       padding: 12px;
                                       border: 1px solid ${status === 'invalid' || status === 'missing' ? '#f44336' : '#ddd'};
                                       border-radius: 6px;
                                       font-size: 14px;
                                       background: ${status === 'missing' ? '#fff3e0' : '#f9f9f9'};
                                       color: #333;
                                   " readonly />
                        `}
                        ${validation && !validation.isValid && validation.error ? `
                            <div style="margin-top: 4px; color: #f44336; font-size: 12px;">
                                ${validation.error}
                            </div>
                        ` : ''}
                    </div>
                `;
            });

            return html;
        };

        //
        // Part 18:  This function gets called after the FaceTec SDK is completely done.  There are no parameters because you have already been passed all data in the processSessionWhileFaceTecSDKWaits function and have already handled all of your own results.
        //
        this.onFaceTecSDKCompletelyDone = function () {
            //
            // DEVELOPER NOTE:  onFaceTecSDKCompletelyDone() is called after the Session has completed or you signal the FaceTec SDK with cancel().
            // Calling a custom function on the Sample App Controller is done for demonstration purposes to show you that here is where you get control back from the FaceTec SDK.
            //
            // If the Photo ID Scan was processed get the success result from isCompletelyDone
            if (_this.latestIDScanResult != null) {
                _this.success = _this.latestIDScanResult.isCompletelyDone;
            }
            // Log success message
            // if (_this.success) {
            //     DeveloperStatusMessages.logMessage("Id Scan Complete");
            // }
            // // If enrollment was not successful, clear the enrollment identifier
            // else {
            //     _this.sampleAppControllerReference.clearLatestEnrollmentIdentifier();
            // }
            _this.sampleAppControllerReference.onComplete(_this.latestSessionResult, _this.latestIDScanResult, _this.latestNetworkRequest.status);
        };
        // Helper function to ensure the session is only cancelled once
        this.cancelDueToNetworkError = function (networkErrorMessage, faceTecScanResultCallback) {
            if (_this.cancelledDueToNetworkError === false) {
                console.error(networkErrorMessage);
                _this.cancelledDueToNetworkError = true;
                faceTecScanResultCallback.cancel();
            }
        };
        //
        // DEVELOPER NOTE:  This public convenience method is for demonstration purposes only so the Sample App can get information about what is happening in the processor.
        // In your code, you may not even want or need to do this.
        //
        this.isSuccess = function () {
            return _this.success;
        };
        //
        // DEVELOPER NOTE:  These properties are for demonstration purposes only so the Sample App can get information about what is happening in the processor.
        // In the code in your own App, you can pass around signals, flags, intermediates, and results however you would like.
        //
        this.success = false;
        this.sampleAppControllerReference = sampleAppControllerReference;
        this.latestSessionResult = null;
        this.latestIDScanResult = null;
        this.cancelledDueToNetworkError = false;
        // In v9.2.2+, configure the messages that will be displayed to the User in each of the possible cases.
        // Based on the internal processing and decision logic about how the flow gets advanced, the FaceTec SDK will use the appropriate, configured message.
        FaceTecSDK.FaceTecCustomization.setIDScanUploadMessageOverrides("Uploading<br/>Encrypted<br/>ID Scan", // Upload of ID front-side has started.
        "Still Uploading...<br/>Slow Connection", // Upload of ID front-side is still uploading to Server after an extended period of time.
        "Upload Complete", // Upload of ID front-side to the Server is complete.
        "Processing<br/>ID Scan", // Upload of ID front-side is complete and we are waiting for the Server to finish processing and respond.
        "Uploading<br/>Encrypted<br/>Back of ID", // Upload of ID back-side has started.
        "Still Uploading...<br/>Slow Connection", // Upload of ID back-side is still uploading to Server after an extended period of time.
        "Upload Complete", // Upload of ID back-side to Server is complete.
        "Processing<br/>Back of ID", // Upload of ID back-side is complete and we are waiting for the Server to finish processing and respond.
        "Uploading<br/>Your Confirmed Info", // Upload of User Confirmed Info has started.
        "Still Uploading...<br/>Slow Connection", // Upload of User Confirmed Info is still uploading to Server after an extended period of time.
        "Info Saved", // Upload of User Confirmed Info to the Server is complete.
        "Processing" // Upload of User Confirmed Info is complete and we are waiting for the Server to finish processing and respond.
        );
        //
        // Part 1:  Starting the FaceTec Session
        //
        // Required parameters:
        // - FaceTecIDScanProcessor:  A class that implements FaceTecIDScanProcessor, which handles the IDScan when the User completes an ID Scan.  In this example, "this" implements the class.
        // - sessionToken:  A valid Session Token you just created by calling your API to get a Session Token from the Server SDK.
        //
        new FaceTecSDK.FaceTecSession(this, sessionToken);
    }
    return PhotoIDMatchProcessor;
}());
var PhotoIDMatchProcessor = PhotoIDMatchProcessor;

// Export the PhotoIDMatchProcessor class for use in Node.js/webpack environments
module.exports = PhotoIDMatchProcessor;
