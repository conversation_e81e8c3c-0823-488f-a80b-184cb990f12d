!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("index",[],t):"object"==typeof exports?exports.index=t():e.index=t()}(this,(()=>{return e={0:(e,t,a)=>{const s=a(216),o=a(599),n=a(719);e.exports=class{constructor(){this.faceTecRepository=new s}async execute({idScanResult:e,deviceKey:t,additionalHeaders:a={},onProgress:s=null}){const o=performance.now();try{n.logMessage("Starting PostIDScanOnlyUseCase execution"),n.logMessage("Preparing scan data...");const r=this.prepareScanData(e);n.logData("Scan Data Keys",Object.keys(r)),n.logMessage("Preparing headers...");const i=this.prepareHeaders(e,t,a);n.logData("Request Headers",Object.keys(i)),n.logMessage("Validating scan data..."),this.faceTecRepository.validateScanData(r),n.logSuccess("Scan data validation passed"),n.logMessage("Submitting to repository...");const c=await this.faceTecRepository.submitIDScan(r,i,s);n.logMessage("Processing response...");const l=this.processResponse(c),d=performance.now();return n.logPerformance("PostIDScanOnlyUseCase.execute",o,d),n.logSuccess(`UseCase completed successfully: ${l.success}`),l}catch(e){const t=performance.now();throw n.logPerformance("PostIDScanOnlyUseCase.execute (failed)",o,t),n.logError("PostIDScanOnlyUseCase - execute error",e),e}}prepareScanData(e){const t={idScan:e.idScan,enableConfirmInfo:!0};return e.frontImages&&e.frontImages[0]&&(t.idScanFrontImage=e.frontImages[0]),e.backImages&&e.backImages[0]&&(t.idScanBackImage=e.backImages[0]),t}prepareHeaders(e,t,a){const s={};return t&&(s["X-Device-Key"]=t),e.sessionId&&(s["X-User-Agent"]=FaceTecSDK.createFaceTecAPIUserAgentString(e.sessionId)),a.Authorization&&(s.Authorization=a.Authorization),a["X-Session-Id"]&&(s["X-Session-Id"]=a["X-Session-Id"]),a["X-Ekyc-Token"]&&(s["X-Ekyc-Token"]=a["X-Ekyc-Token"]),a.correlationid&&(s.correlationid=a.correlationid),s["X-Tid"]=o.getUniqueId(),s}processResponse(e){let t=null;if(e.originalResponse&&e.originalResponse.data&&e.originalResponse.data.documentData)try{const a=JSON.parse(e.originalResponse.data.documentData);n.logData("Parsed documentData",a),t=this.extractOcrDataFromDocumentData(a),t?n.logData("Extracted OCR Data",Object.keys(t)):n.logMessage("No valid OCR fields found in documentData")}catch(e){n.logError("Failed to parse documentData JSON:",e),n.logMessage("No OCR data found in response")}else n.logMessage("No documentData found in response");return{success:!0===e.wasProcessed&&!1===e.error,scanResultBlob:e.scanResultBlob,originalResponse:e.originalResponse,errorMessage:e.errorMessage,userOcrValue:t}}extractOcrDataFromDocumentData(e){if(!e||!e.scannedValues||!e.scannedValues.groups)return n.logMessage("Invalid documentData structure"),null;const t={nationalId:null,titleTh:null,firstNameTh:null,middleNameTh:null,lastNameTh:null,titleEn:null,firstNameEn:null,middleNameEn:null,lastNameEn:null,dateOfBirth:null,dateOfIssue:null,dateOfExpiry:null,laserId:null},a={idNumber:"nationalId",firstName:"_rawFirstName",fullname:"_rawFullname",fullName:"_rawFullname",lastName:"lastNameEn",dateOfBirth:"dateOfBirth",dateOfIssue:"dateOfIssue",dateOfExpiration:"dateOfExpiry",customField1:"laserId"},s={_rawFirstName:null,_rawFullname:null};return e.scannedValues.groups.forEach((e=>{e.fields&&Array.isArray(e.fields)&&e.fields.forEach((e=>{if(e.fieldKey&&e.value&&a[e.fieldKey]){const o=a[e.fieldKey];o.startsWith("_raw")?(s[o]=e.value,n.logData(`Stored raw ${e.fieldKey}`,e.value)):(t[o]=e.value,n.logData(`Mapped ${e.fieldKey} -> ${o}`,e.value))}}))})),s._rawFullname&&this.parseThaiNameFields(s._rawFullname,t),s._rawFirstName&&this.parseEnglishNameFields(s._rawFirstName,t),this.convertDateFields(t),this.addFieldEditabilityMetadata(t),Object.values(t).some((e=>null!==e))?t:(n.logMessage("No valid OCR fields found in documentData structure"),null)}parseThaiNameFields(e,t){if(!e||"string"!=typeof e)return void n.logMessage("Invalid fullname for Thai parsing");const a=e.trim().split(/\s+/);n.logData("Thai name parts",a),a.length>=1&&(t.titleTh=a[0],n.logData("Extracted titleTh",t.titleTh)),a.length>=2&&(t.firstNameTh=a[1],n.logData("Extracted firstNameTh",t.firstNameTh)),a.length>=3&&(t.lastNameTh=a.slice(2).join(" "),n.logData("Extracted lastNameTh",t.lastNameTh))}parseEnglishNameFields(e,t){if(!e||"string"!=typeof e)return void n.logMessage("Invalid firstName for English parsing");const a=e.trim().split(/\s+/);n.logData("English name parts",a),a.length>=1&&(t.titleEn=a[0],n.logData("Extracted titleEn",t.titleEn)),a.length>=2&&(t.firstNameEn=a.slice(1).join(" "),n.logData("Extracted firstNameEn",t.firstNameEn))}convertDateFields(e){["dateOfBirth","dateOfIssue","dateOfExpiry"].forEach((t=>{if(e[t]){const a=this.convertThaiDateFormat(e[t]);a?(n.logData(`Converted ${t}`,`${e[t]} -> ${a}`),e[t]=a):n.logError(`Failed to convert date field ${t}`,e[t])}}))}convertThaiDateFormat(e){if(!e||"string"!=typeof e)return null;try{const t={"ม.ค.":"01",มกราคม:"01","ม.ค":"01","ก.พ.":"02",กุมภาพันธ์:"02","ก.พ":"02","มี.ค.":"03",มีนาคม:"03","มี.ค":"03","เม.ย.":"04",เมษายน:"04","เม.ย":"04","พ.ค.":"05",พฤษภาคม:"05","พ.ค":"05","มิ.ย.":"06",มิถุนายน:"06","มิ.ย":"06","ก.ค.":"07",กรกฎาคม:"07","ก.ค":"07","ส.ค.":"08",สิงหาคม:"08","ส.ค":"08","ก.ย.":"09",กันยายน:"09","ก.ย":"09","ต.ค.":"10",ตุลาคม:"10","ต.ค":"10","พ.ย.":"11",พฤศจิกายน:"11","พ.ย":"11","ธ.ค.":"12",ธันวาคม:"12","ธ.ค":"12"},a=e.trim();if(/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(a))return n.logData("Date already in standard format",a),a;const s=a.split(/\s+/);if(s.length>=3){const a=s[0].padStart(2,"0"),o=s[1],r=s[2],i=t[o];if(i&&/^\d{4}$/.test(r)){const t=`${a}/${i}/${r}`;return n.logData("Thai date conversion successful",`${e} -> ${t}`),t}}const o=a.split("/");if(3===o.length){const a=o[0].padStart(2,"0"),s=o[1],r=o[2],i=t[s];if(i&&/^\d{4}$/.test(r)){const t=`${a}/${i}/${r}`;return n.logData("Thai date conversion (slash format) successful",`${e} -> ${t}`),t}}return n.logError("Unable to parse Thai date format",e),null}catch(e){return n.logError("Error converting Thai date format",e),null}}addFieldEditabilityMetadata(e){const t=["titleTh","firstNameTh","lastNameTh","titleEn","firstNameEn","lastNameEn","dateOfBirth","dateOfIssue","dateOfExpiry"],a=["nationalId","laserId"];e._fieldMetadata={editableFields:t,readOnlyFields:a,dateFields:["dateOfBirth","dateOfIssue","dateOfExpiry"],requiredFields:["nationalId","titleTh","firstNameTh","lastNameTh","dateOfBirth","dateOfExpiry"],optionalFields:["middleNameTh","middleNameEn","titleEn","firstNameEn","lastNameEn","dateOfIssue","laserId"]},Object.keys(e).forEach((s=>{"_fieldMetadata"!==s&&null!==e[s]&&(e[`_${s}Properties`]={isEditable:t.includes(s),isReadOnly:a.includes(s),isDate:["dateOfBirth","dateOfIssue","dateOfExpiry"].includes(s),isRequired:["nationalId","titleTh","firstNameTh","lastNameTh","dateOfBirth","dateOfExpiry"].includes(s),originalValue:e[s],hasBeenModified:!1})})),n.logData("Added field editability metadata",{editableCount:t.length,readOnlyCount:a.length,dateFieldCount:3})}}},103:e=>{e.exports=class{constructor(e){this.data=e}getToken(){return this.data?.token||null}getEkycToken(){return this.data?.data?.ekycToken?this.data.data.ekycToken:this.data?.ekycToken||null}getExpiresAt(){return this.data?.expiresAt||null}getCode(){return this.data?.code||null}getDescription(){return this.data?.description||null}isValid(){return!!this.getEkycToken()}toJSON(){return this.data}}},161:(e,t,a)=>{const s=a(103);e.exports=class{constructor(e){this.authApiDataSource=e}async getSessionToken(e={}){const t=await this.authApiDataSource.getSessionToken(e);return new s(t)}async getFaceTecSessionTokenWithEkycToken(e={}){const t=await this.authApiDataSource.getFaceTecSessionTokenWithEkycToken(e);return new s(t)}}},189:e=>{e.exports=class{constructor(e,t="USD"){this.amount=e,this.currencyCode=t}format(){return new Intl.NumberFormat("en-US",{style:"currency",currency:this.currencyCode}).format(this.amount)}getAmount(){return this.amount}getCurrencyCode(){return this.currencyCode}}},216:(e,t,a)=>{const s=a(518),o=a(405),n=a(489),r=a(777);e.exports=class{constructor(){this.idScanDataSource=new s,this.idScanFrontDataSource=new o,this.idScanBackDataSource=new n,this.livenessCheckDataSource=new r}async submitIDScan(e,t={},a=null){try{return await this.idScanDataSource.postIDScanOnly(e,t,a)}catch(e){throw console.error("FaceTecRepository - submitIDScan error:",e),e}}async submitIDScanFront(e,t={},a=null){const s=performance.now();try{console.log("=== FACETEC REPOSITORY - FRONT ID SCAN START ==="),this.logFrontScanRequest(e,t),this.validateAndLogFrontScanData(e),console.log("📤 Calling front data source...");const o=await this.idScanFrontDataSource.postIDScanFront(e,t,a);return this.logFrontScanResponse(o,s),console.log("=== FACETEC REPOSITORY - FRONT ID SCAN SUCCESS ==="),o}catch(e){const t=performance.now()-s;throw console.error("=== FACETEC REPOSITORY - FRONT ID SCAN ERROR ==="),console.error("❌ Error Type:",e.constructor.name),console.error("❌ Error Message:",e.message),console.error("❌ Error Stack:",e.stack),console.error("⏱️ Duration before error:",`${t.toFixed(2)}ms`),e.response&&(console.error("📥 Error Response Status:",e.response.status),console.error("📥 Error Response Headers:",e.response.headers),console.error("📥 Error Response Data:",e.response.data)),e.request&&console.error("📤 Failed Request Details:",{url:e.request.url||"Unknown URL",method:e.request.method||"Unknown Method",timeout:e.request.timeout||"No timeout set"}),console.error("=== END FRONT ID SCAN ERROR LOG ==="),e}}async submitIDScanBack(e,t={},a=null){const s=performance.now();try{console.log("=== FACETEC REPOSITORY - BACK ID SCAN START ==="),this.logBackScanRequest(e,t),this.validateAndLogBackScanData(e),console.log("📤 Calling back data source...");const o=await this.idScanBackDataSource.postIDScanBack(e,t,a);return this.logBackScanResponse(o,s),console.log("=== FACETEC REPOSITORY - BACK ID SCAN SUCCESS ==="),o}catch(e){const t=performance.now()-s;throw console.error("=== FACETEC REPOSITORY - BACK ID SCAN ERROR ==="),console.error("❌ Error Type:",e.constructor.name),console.error("❌ Error Message:",e.message),console.error("❌ Error Stack:",e.stack),console.error("⏱️ Duration before error:",`${t.toFixed(2)}ms`),e.response&&(console.error("📥 Error Response Status:",e.response.status),console.error("📥 Error Response Headers:",e.response.headers),console.error("📥 Error Response Data:",e.response.data)),e.request&&console.error("📤 Failed Request Details:",{url:e.request.url||"Unknown URL",method:e.request.method||"Unknown Method",timeout:e.request.timeout||"No timeout set"}),console.error("=== END BACK ID SCAN ERROR LOG ==="),e}}async submitLivenessCheck(e,t={},a=null){try{return await this.livenessCheckDataSource.postLivenessCheck(e,t,a)}catch(e){throw console.error("FaceTecRepository - submitLivenessCheck error:",e),e}}validateScanData(e){if(!e)throw new Error("Scan data is required");if(!e.idScan)throw new Error("ID scan data is required");return!0}validateFrontScanData(e){return this.idScanFrontDataSource.validateFrontScanData(e)}validateBackScanData(e){return this.idScanBackDataSource.validateBackScanData(e)}validateLivenessData(e){if(!e)throw new Error("Liveness data is required");if(!e.faceScan)throw new Error("Face scan data is required");return!0}logFrontScanRequest(e,t){console.log("📤 FRONT SCAN REQUEST DETAILS:"),console.log("🎯 Target Endpoint: /api/match-3d-2d-idscan/front"),console.log("📋 Request Headers:"),Object.keys(t).forEach((e=>{const a=t[e];e.toLowerCase().includes("token")||e.toLowerCase().includes("auth")?console.log(`  ${e}: ${this.maskSensitiveData(a)}`):console.log(`  ${e}: ${a}`)})),this.logScanDataStructure(e,"FRONT")}logBackScanRequest(e,t){console.log("📤 BACK SCAN REQUEST DETAILS:"),console.log("🎯 Target Endpoint: /api/match-3d-2d-idscan/back"),console.log("📋 Request Headers:"),Object.keys(t).forEach((e=>{const a=t[e];e.toLowerCase().includes("token")||e.toLowerCase().includes("auth")?console.log(`  ${e}: ${this.maskSensitiveData(a)}`):console.log(`  ${e}: ${a}`)})),this.logScanDataStructure(e,"BACK")}validateAndLogFrontScanData(e){console.log("🔍 FRONT SCAN DATA VALIDATION:");const t={hasIdScan:!!e?.idScan,hasFrontImage:!!e?.idScanFrontImage,hasBackImage:!!e?.idScanBackImage,hasEnableConfirmInfo:void 0!==e?.enableConfirmInfo,enableConfirmInfoValue:e?.enableConfirmInfo};console.log("✅ Validation Results:",t);const a=[];t.hasIdScan||a.push("❌ Missing idScan data"),t.hasFrontImage||a.push("❌ Missing idScanFrontImage (required for front scan)"),t.hasBackImage&&console.log("ℹ️ Back image present in front scan request (may be intentional)"),a.length>0?(console.error("🚨 VALIDATION ISSUES FOUND:"),a.forEach((e=>console.error(e)))):console.log("✅ All required fields present for front scan");try{this.validateFrontScanData(e),console.log("✅ Data source validation passed")}catch(e){throw console.error("❌ Data source validation failed:",e.message),e}}validateAndLogBackScanData(e){console.log("🔍 BACK SCAN DATA VALIDATION:");const t={hasIdScan:!!e?.idScan,hasFrontImage:!!e?.idScanFrontImage,hasBackImage:!!e?.idScanBackImage,hasEnableConfirmInfo:void 0!==e?.enableConfirmInfo,enableConfirmInfoValue:e?.enableConfirmInfo};console.log("✅ Validation Results:",t);const a=[];t.hasIdScan||a.push("❌ Missing idScan data"),t.hasBackImage||a.push("❌ Missing idScanBackImage (required for back scan)"),t.hasFrontImage||console.log("ℹ️ Front image missing in back scan request (may be optional)"),a.length>0?(console.error("🚨 VALIDATION ISSUES FOUND:"),a.forEach((e=>console.error(e)))):console.log("✅ All required fields present for back scan");try{this.validateBackScanData(e),console.log("✅ Data source validation passed")}catch(e){throw console.error("❌ Data source validation failed:",e.message),e}}logScanDataStructure(e,t){if(console.log(`📊 ${t} SCAN DATA STRUCTURE:`),!e)return void console.error("❌ Scan data is null or undefined");const a={keys:Object.keys(e),hasIdScan:!!e.idScan,hasIdScanFrontImage:!!e.idScanFrontImage,hasIdScanBackImage:!!e.idScanBackImage,enableConfirmInfo:e.enableConfirmInfo};console.log("📋 Basic Structure:",a),e.idScanFrontImage&&console.log("🖼️ Front Image Details:",this.getImageDataInfo(e.idScanFrontImage)),e.idScanBackImage&&console.log("🖼️ Back Image Details:",this.getImageDataInfo(e.idScanBackImage)),e.idScan&&console.log("🆔 ID Scan Data:",this.getTruncatedData(e.idScan));const s=JSON.stringify(e);console.log(`📏 Total Request Size: ${s.length} characters`),s.length>1e3?(console.log("📄 Request Body (truncated):"),console.log(this.smartTruncate(s,1e3))):console.log("📄 Request Body:",s)}logFrontScanResponse(e,t){const a=performance.now()-t;console.log("📥 FRONT SCAN RESPONSE:"),console.log(`⏱️ Duration: ${a.toFixed(2)}ms`),console.log("✅ Response Structure:",{wasProcessed:e.wasProcessed,error:e.error,hasScanResultBlob:!!e.scanResultBlob,hasOriginalResponse:!!e.originalResponse,hasErrorMessage:!!e.errorMessage}),e.scanResultBlob&&console.log(`📦 Scan Result Blob Length: ${e.scanResultBlob.length} characters`),e.originalResponse&&console.log("📋 Original Response Keys:",Object.keys(e.originalResponse))}logBackScanResponse(e,t){const a=performance.now()-t;if(console.log("📥 BACK SCAN RESPONSE:"),console.log(`⏱️ Duration: ${a.toFixed(2)}ms`),console.log("✅ Response Structure:",{wasProcessed:e.wasProcessed,error:e.error,hasScanResultBlob:!!e.scanResultBlob,hasOriginalResponse:!!e.originalResponse,hasErrorMessage:!!e.errorMessage,hasOcrData:!!e.originalResponse?.data?.documentData}),e.scanResultBlob&&console.log(`📦 Scan Result Blob Length: ${e.scanResultBlob.length} characters`),e.originalResponse?.data?.documentData){console.log("📄 OCR Document Data Present: Yes");try{const t=JSON.parse(e.originalResponse.data.documentData);console.log("📊 OCR Data Structure:",{hasScannedValues:!!t.scannedValues,groupCount:t.scannedValues?.groups?.length||0})}catch(e){console.error("❌ Failed to parse OCR document data:",e.message)}}}maskSensitiveData(e){return e&&"string"==typeof e?e.length<=8?"*".repeat(e.length):e.substring(0,4)+"*".repeat(e.length-8)+e.substring(e.length-4):String(e)}getImageDataInfo(e){if(!e||"string"!=typeof e)return{type:"invalid",length:0};const t={length:e.length,type:"unknown"};if(e.startsWith("data:image/")){const a=e.match(/data:image\/([^;]+)/);a&&(t.type=a[1])}else e.startsWith("/9j/")?t.type="jpeg (base64)":e.startsWith("iVBORw0KGgo")?t.type="png (base64)":t.type="base64 data";return t}getTruncatedData(e){if("string"==typeof e)return this.smartTruncate(e,200);if("object"==typeof e&&null!==e){const t=JSON.stringify(e);return t.length>200?this.smartTruncate(t,200):e}return e}smartTruncate(e,t){if(!e||e.length<=t)return e;const a=Math.floor(t/2)-10,s=e.substring(0,a),o=e.substring(e.length-a);return`${s}...[${e.length-t} chars truncated]...${o}`}}},266:(e,t,a)=>{const s=a(307),o=a(719);e.exports=function(e,t,a,n){var r=this;this.latestNetworkRequest=new XMLHttpRequest,this.deviceKey=a||null,this.additionalHeaders=n||{},this.postLivenessCheckUseCase=new s,this.processSessionResultWhileFaceTecSDKWaits=function(e,t){if(r.latestSessionResult=e,e.status!==FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully)return r.latestNetworkRequest.abort(),r.latestNetworkRequest=new XMLHttpRequest,void t.cancel();r.executeLivenessCheckUseCase(e,t)},this.executeLivenessCheckUseCase=async function(e,t){try{const a=function(e){t.uploadProgress(e)},s=await r.postLivenessCheckUseCase.execute({sessionResult:e,deviceKey:r.deviceKey,additionalHeaders:r.additionalHeaders,onProgress:a});s.success?(FaceTecSDK.FaceTecCustomization.setOverrideResultScreenSuccessMessage("Face Scanned\n3D Liveness Proven"),t.proceedToNextStep(s.scanResultBlob)):r.cancelDueToNetworkError(s.errorMessage||"Unexpected API response, cancelling out.",t)}catch(e){console.error("LivenessCheckProcessor - executeLivenessCheckUseCase error:",e),r.cancelDueToNetworkError(e.message||"Exception while handling API response, cancelling out.",t)}},this.onFaceTecSDKCompletelyDone=function(){null!==r.latestSessionResult&&(r.success=r.latestSessionResult.isCompletelyDone),r.success&&o.logMessage("Liveness Check Complete"),r.sampleAppControllerReference.onComplete(r.latestSessionResult,200)},this.cancelDueToNetworkError=function(e,t){!1===r.cancelledDueToNetworkError&&(console.error(e),r.cancelledDueToNetworkError=!0,t.cancel())},this.isSuccess=function(){return r.success},this.success=!1,this.sampleAppControllerReference=t,this.latestSessionResult=null,this.cancelledDueToNetworkError=!1,new FaceTecSDK.FaceTecSession(this,e)}},307:(e,t,a)=>{const s=a(216);e.exports=class{constructor(){this.faceTecRepository=new s}async execute(e){try{const{sessionResult:t,deviceKey:s,additionalHeaders:o,onProgress:n}=e;console.log("=== POST LIVENESS CHECK USE CASE - STARTING ==="),console.log("Input params:",{hasSessionResult:!!t,deviceKey:s,additionalHeaders:o,hasOnProgress:"function"==typeof n}),t&&console.log("Session result details:",{sessionId:t.sessionId,hasFaceScan:!!t.faceScan,hasAuditTrail:!!t.auditTrail&&t.auditTrail.length>0,hasLowQualityAuditTrail:!!t.lowQualityAuditTrail&&t.lowQualityAuditTrail.length>0,auditTrailLength:t.auditTrail?t.auditTrail.length:0,lowQualityAuditTrailLength:t.lowQualityAuditTrail?t.lowQualityAuditTrail.length:0});const r={...o};if(s&&(r["X-Device-Key"]=s),t.sessionId&&"undefined"!=typeof FaceTecSDK&&(r["X-User-Agent"]=FaceTecSDK.createFaceTecAPIUserAgentString(t.sessionId)),r["X-Tid"])console.log("✅ X-Tid header already present:",r["X-Tid"]);else{const e=a(599);r["X-Tid"]=e.getUniqueId(),console.log("🔧 Generated X-Tid header:",r["X-Tid"])}console.log("Prepared headers:",JSON.stringify(r,null,2));const i={faceScan:t.faceScan,auditTrailImage:t.auditTrail[0],lowQualityAuditTrailImage:t.lowQualityAuditTrail[0],sessionId:t.sessionId,function:"liveness"};console.log("=== POST LIVENESS CHECK USE CASE - PREPARED DATA ==="),console.log("Liveness data keys:",Object.keys(i)),console.log("Liveness data structure:",{function:i.function,sessionId:i.sessionId,hasFaceScan:!!i.faceScan,hasAuditTrailImage:!!i.auditTrailImage,hasLowQualityAuditTrailImage:!!i.lowQualityAuditTrailImage,faceScanLength:i.faceScan?i.faceScan.length:0,auditTrailImageLength:i.auditTrailImage?i.auditTrailImage.length:0,lowQualityAuditTrailImageLength:i.lowQualityAuditTrailImage?i.lowQualityAuditTrailImage.length:0}),console.log("Calling repository.submitLivenessCheck...");const c=await this.faceTecRepository.submitLivenessCheck(i,r,n);if(console.log("=== POST LIVENESS CHECK USE CASE - REPOSITORY RESPONSE ==="),console.log("Repository response:",JSON.stringify(c,null,2)),!0===c.wasProcessed&&!1===c.error){const e={success:!0,scanResultBlob:c.scanResultBlob};return console.log("✅ USE CASE SUCCESS - Returning:",e),e}{const e={success:!1,errorMessage:c.errorMessage||"Server returned an error."};return console.log("❌ USE CASE ERROR - Returning:",e),e}}catch(e){throw console.error("❌ PostLivenessCheckUseCase error:",e),e}}}},345:e=>{e.exports=class{static validateAll(e){if(!e)return{isValid:!1,errors:["No OCR data provided"],fieldValidations:{}};const t={},a=[],s=this.validateNationalId(e.nationalId);t.nationalId=s,s.isValid||a.push(`National ID: ${s.error}`),t.titleTh=this.validateRequiredField(e.titleTh,"Title (TH)"),t.firstNameTh=this.validateRequiredField(e.firstNameTh,"First Name (TH)"),t.middleNameTh=this.validateOptionalField(e.middleNameTh,"Middle Name (TH)"),t.lastNameTh=this.validateRequiredField(e.lastNameTh,"Last Name (TH)"),t.titleEn=this.validateRequiredField(e.titleEn,"Title (EN)"),t.firstNameEn=this.validateRequiredField(e.firstNameEn,"First Name (EN)"),t.middleNameEn=this.validateOptionalField(e.middleNameEn,"Middle Name (EN)"),t.lastNameEn=this.validateRequiredField(e.lastNameEn,"Last Name (EN)"),t.dateOfBirth=this.validateDate(e.dateOfBirth,"Date of Birth"),t.dateOfIssue=this.validateDate(e.dateOfIssue,"Date of Issue"),t.dateOfExpiry=this.validateDate(e.dateOfExpiry,"Date of Expiry"),t.laserId=this.validateLaserId(e.laserId),Object.values(t).forEach((e=>{!e.isValid&&e.error&&a.push(e.error)}));const o=this.validateIdCardExpiry(e.dateOfExpiry);return o.isValid||a.push(o.error),{isValid:0===a.length,errors:a,fieldValidations:t,isExpired:!o.isValid}}static validateNationalId(e){if(!e||"string"!=typeof e)return{isValid:!1,error:"National ID is required",status:"missing"};const t=e.replace(/[\s-]/g,"");return/^\d{13}$/.test(t)?this.validateThaiIdChecksum(t)?{isValid:!0,status:"valid"}:{isValid:!1,error:"Invalid National ID checksum",status:"invalid"}:{isValid:!1,error:"National ID must be 13 digits",status:"invalid"}}static validateThaiIdChecksum(e){if(13!==e.length)return!1;let t=0;for(let a=0;a<12;a++)t+=parseInt(e[a])*(13-a);const a=t%11;return(a<2?a:11-a)===parseInt(e[12])}static validateRequiredField(e,t){return e&&"string"==typeof e&&""!==e.trim()?{isValid:!0,status:"valid"}:{isValid:!1,error:`${t} is required`,status:"missing"}}static validateOptionalField(e,t){return e&&"string"==typeof e&&""!==e.trim()?{isValid:!0,status:"valid"}:{isValid:!0,status:"optional"}}static validateDate(e,t){if(!e||"string"!=typeof e)return{isValid:!1,error:`${t} is required`,status:"missing"};const a=this.parseDate(e);return!a||isNaN(a.getTime())?{isValid:!1,error:`${t} has invalid date format`,status:"invalid"}:{isValid:!0,status:"valid",parsedDate:a}}static parseDate(e){if(!e)return null;const t=[/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,/^(\d{1,2})-(\d{1,2})-(\d{4})$/,/^(\d{4})-(\d{1,2})-(\d{1,2})$/,/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/];for(const a of t){const t=e.match(a);if(t){let e,s,o;a.source.startsWith("^(\\d{4})")?(o=parseInt(t[1]),s=parseInt(t[2])-1,e=parseInt(t[3])):(e=parseInt(t[1]),s=parseInt(t[2])-1,o=parseInt(t[3]));const n=new Date(o,s,e);if(n.getFullYear()===o&&n.getMonth()===s&&n.getDate()===e)return n}}return null}static validateLaserId(e){return e&&"string"==typeof e&&""!==e.trim()?/^[A-Za-z0-9]+$/.test(e.trim())?{isValid:!0,status:"valid"}:{isValid:!1,error:"Laser ID contains invalid characters",status:"invalid"}:{isValid:!1,error:"Laser ID is required",status:"missing"}}static validateIdCardExpiry(e){const t=this.validateDate(e,"Expiry Date");if(!t.isValid)return t;const a=t.parsedDate,s=new Date;return s.setHours(0,0,0,0),a<s?{isValid:!1,error:"ID card has expired",status:"expired"}:{isValid:!0,status:"valid"}}static getStatusIcon(e){switch(e){case"valid":return"✓";case"invalid":return"✗";case"missing":return"⚠";case"optional":return"○";case"expired":return"⏰";default:return"?"}}static getStatusColor(e){switch(e){case"valid":return"#4CAF50";case"invalid":return"#F44336";case"missing":return"#FF9800";case"optional":return"#9E9E9E";case"expired":return"#FF5722";default:return"#757575"}}}},347:(e,t,a)=>{const s=a(592),o=a(863),n=a(995),r=a(985),i=a(161),c=a(548),{TokenStorage:l}=a(411),{UuidGenerator:d}=a(411),u=a(382),g=new i(new c),p=new s,f=new o,h=new n(g),m=new r(g),S=a(955),D=a(453),y=async(e={},t=!0)=>{try{e["X-Session-Id"]&&l.storeSessionId(e["X-Session-Id"]);const a=await h.execute(e),s=a.toJSON();if(t){const e=a.getEkycToken();e&&l.storeEkycToken(e)}return s}catch(e){throw console.error("Error getting session token:",e),e}},I=async(e={},t=!0)=>{try{const a=l.getSessionId();a&&!e["X-Session-Id"]&&(e={...e,"X-Session-Id":a});const s=(await m.execute(e)).toJSON();if(s.faceTecInitialized=!1,t&&s&&"CUS-KYC-1000"===s.code&&s.data&&s.data.deviceKey&&s.data.encryptionKey)try{await u.initializeFaceTec(s.data.deviceKey,s.data.encryptionKey),console.log("FaceTec SDK initialized successfully"),s.faceTecInitialized=!0}catch(e){console.error("Error initializing FaceTec SDK:",e),s.faceTecError=e.message||"Failed to initialize FaceTec SDK"}return s}catch(e){throw console.error("Error getting FaceTec session token with eKYC token:",e),e}},k=async(e={},t=null,s=null)=>{try{if(!t)throw new Error("deviceKey parameter is required for Photo ID Scan");if(!s.faceTecInitialized)throw new Error("FaceTec SDK not initialized properly");const o=await u.loadFaceTecSDK();o.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources"),o.setImagesDirectory("/core-sdk/FaceTec_images");const n={onComplete:(e,t,a)=>({sessionResult:e,idScanResult:t,networkResponseStatus:a})},r={"X-Session-Id":e["X-Session-Id"]||s.data?.sessionId,"X-Ekyc-Token":s.data?.ekycToken||l.getEkycToken(),correlationid:e.correlationid||a(411).UuidGenerator.getUniqueId()},i=new S(s.data.sessionFaceTec,n,t,r);return new Promise(((e,t)=>{n.onComplete=(a,s,o)=>{i.isSuccess()?e({sessionResult:a,idScanResult:s,networkResponseStatus:o}):t(new Error("ID scan failed"))}}))}catch(e){throw console.error("Error performing photo ID scan:",e),e}};e.exports={formatCurrency:(e,t="USD")=>p.execute(e,t),greet:e=>f.execute(e),getSessionToken:y,getStoredEkycToken:()=>l.getEkycToken(),clearEkycToken:()=>l.removeEkycToken(),getFaceTecSessionTokenWithEkycToken:I,performPhotoIDScan:k,initEkyc:async(e={})=>{const{sessionId:t,token:s,environment:o="development",language:n="en",initCallback:r}=e;if(!t)throw new Error("sessionId is required for eKYC initialization");if(!s)throw new Error("token is required for eKYC initialization");try{console.log("🚀 Initializing eKYC SDK with sessionId:",t);const{UuidGenerator:e}=a(411);let i=l.getToken("ekyc_device_id");i||(i=e.getUniqueId(),l.storeToken("ekyc_device_id",i)),l.storeToken("ekyc_session_id",t),l.storeToken("ekyc_api_token",s),l.storeToken("ekyc_environment",o),l.storeToken("ekyc_language",n);const c={Authorization:`Bearer ${s}`,"X-Session-Id":t,"X-Ekyc-Device-Info":`browser|${i}|${"undefined"!=typeof window?window.location.origin:"unknown"}|${n}|${n.toUpperCase()}`};console.log("📡 Getting session token...");const d=await y(c,!0);console.log("🎭 Getting FaceTec session token and initializing...");const u=await I(c,!0),g={success:!0,sessionToken:d,faceTecToken:u,environment:o,language:n,sessionId:t,initialized:!0,faceTecInitialized:u.faceTecInitialized||!1};return console.log("✅ eKYC SDK initialized successfully"),r&&"function"==typeof r&&r(g),g}catch(e){console.error("❌ Error initializing eKYC SDK:",e);const a={success:!1,error:e.message||"Failed to initialize eKYC SDK",environment:o,language:n,sessionId:t,initialized:!1};throw r&&"function"==typeof r&&r(a),e}},ocrIdCard:async(e={})=>{const{checkExpiredIdCard:t=!0,checkDopa:a=!1,enableConfirmInfo:s=!0,callback:o}=e;try{console.log("📄 Starting OCR ID Card scan...");const e=l.getToken("ekyc_session_id"),t=l.getToken("ekyc_device_id")||"unknown";if(!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const a={"X-Session-Id":e,"X-Ekyc-Device-Info":`browser|${t}`,"X-Ekyc-Token":l.getToken("ekyc_token")};console.log("🎭 Ensuring FaceTec SDK is initialized...");const s=await I(a,!0);if(!s.faceTecInitialized)throw new Error("FaceTec SDK not properly initialized");console.log("🔍 Performing ID scan...");const n=await k(a,t,s,ocrResultsCallback),r=n.processor?n.processor.getCallbackResult():null,i={success:!r||r.success,description:r?r.description:"OCR ID Card scan completed successfully",userOcrValue:r?r.userOcrValue:null,userConfirmedValue:r?r.userConfirmedValue:null,dopaResult:r?r.dopaResult:null};return console.log("✅ OCR ID Card scan completed"),console.log("📊 Standardized result structure:",{success:i.success,description:i.description,hasUserOcrValue:!!i.userOcrValue,hasUserConfirmedValue:!!i.userConfirmedValue}),o&&"function"==typeof o&&o(i),i}catch(e){console.error("❌ Error performing OCR ID card scan:",e);const n={success:!1,description:e.message||"Unable to Process",userOcrValue:null,userConfirmedValue:null,dopaResult:null,error:e.message||"Failed to perform OCR ID card scan",checkExpiredIdCard:t,checkDopa:a,enableConfirmInfo:s,scanType:"id_card_ocr",processor:null,callbackResult:null};throw o&&"function"==typeof o&&o(n),e}},ocrIdCardVerifyByFace:async(e={})=>{const{checkExpiredIdCard:t=!0,checkDopa:s=!1,enableConfirmInfo:o=!0,callback:n,ocrResultsCallback:r}=e;try{console.log("📄 Starting OCR ID Card with facial verification scan...");const e=l.getToken("ekyc_session_id"),r=l.getToken("ekyc_device_id")||"unknown";if(!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const i={"X-Session-Id":e,"X-Ekyc-Device-Info":`browser|${r}`,"X-Ekyc-Token":l.getToken("ekyc_token")};console.log("🎭 Ensuring FaceTec SDK is initialized...");const c=await I(i,!0);if(!c.faceTecInitialized)throw new Error("FaceTec SDK not properly initialized");console.log("🔍 Performing ID scan with face verification...");const d=await(async(e={},t=null,s=null)=>{try{if(!t)throw new Error("deviceKey parameter is required for Photo ID Scan");if(!s.faceTecInitialized)throw new Error("FaceTec SDK not initialized properly");const o=await u.loadFaceTecSDK();o.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources"),o.setImagesDirectory("/core-sdk/FaceTec_images");const n={onComplete:(e,t,a)=>({sessionResult:e,idScanResult:t,networkResponseStatus:a})},r={"X-Session-Id":e["X-Session-Id"]||s.data?.sessionId,"X-Ekyc-Token":s.data?.ekycToken||l.getEkycToken(),correlationid:e.correlationid||a(411).UuidGenerator.getUniqueId()},i=new D(s.data.sessionFaceTec,n,t,r);return new Promise(((e,t)=>{let a=!1;i._overlayShownAndCancelled=()=>{a=!0},n.onComplete=(s,o,n)=>{i.isSuccess()?e({sessionResult:s,idScanResult:o,networkResponseStatus:n}):a?e({sessionResult:s,idScanResult:o,networkResponseStatus:n,cancelled:!0}):t(new Error("ID scan failed"))}}))}catch(e){throw console.error("Error performing photo ID scan:",e),e}})(i,r,c),g=d.processor?d.processor.getCallbackResult():null,p={success:!g||g.success,description:g?g.description:"OCR ID Card with face verification completed successfully",userOcrValue:g?g.userOcrValue:null,userConfirmedValue:g?g.userConfirmedValue:null,dopaResult:g?g.dopaResult:null,ocrData:d,checkExpiredIdCard:t,checkDopa:s,enableConfirmInfo:o,sessionId:e,scanType:"id_card_ocr_with_face_verification",processor:d.processor,callbackResult:g};return console.log("✅ OCR ID Card with face verification completed"),console.log("📊 Standardized result structure:",{success:p.success,description:p.description,hasUserOcrValue:!!p.userOcrValue,hasUserConfirmedValue:!!p.userConfirmedValue}),n&&"function"==typeof n&&n(p),p}catch(e){console.error("❌ Error performing OCR ID card with face verification:",e);const a={success:!1,description:e.message||"Failed to perform OCR ID card with face verification",userOcrValue:null,userConfirmedValue:null,dopaResult:null,error:e.message||"Failed to perform OCR ID card with face verification",checkExpiredIdCard:t,checkDopa:s,enableConfirmInfo:o,scanType:"id_card_ocr_with_face_verification",processor:null,callbackResult:null};throw n&&"function"==typeof n&&n(a),e}},ndidVerification:async(e={})=>{const{identifierType:t,identifierValue:a,serviceId:s,ndidVerificationCallback:o}=e;if(!t)throw new Error("identifierType is required for NDID verification");if(!a)throw new Error("identifierValue is required for NDID verification");if(!s)throw new Error("serviceId is required for NDID verification");try{console.log("🆔 Starting NDID verification...");const e=l.getToken("ekyc_session_id");if(l.getToken("ekyc_device_id"),!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const n=`ndid_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;await new Promise((e=>setTimeout(e,2e3)));const r={success:!0,ndidVerified:!0,identifierType:t,identifierValue:a,serviceId:s,sessionId:e,verificationId:n,timestamp:(new Date).toISOString(),ndidResponse:{status:"verified",confidence:.95,details:{identityConfirmed:!0,documentValid:!0,biometricMatch:!0}}};return console.log("✅ NDID verification completed successfully"),o&&"function"==typeof o&&o(r),r}catch(e){console.error("❌ Error performing NDID verification:",e);const n={success:!1,error:e.message||"Failed to perform NDID verification",identifierType:t,identifierValue:a,serviceId:s};throw o&&"function"==typeof o&&o(n),e}},livenessCheck:async(e={})=>{const{livenessCheckCallback:t}=e;try{console.log("👁️ Starting liveness check...");const e=l.getToken("ekyc_session_id"),s=l.getToken("ekyc_device_id")||"unknown";if(!e)throw new Error("eKYC SDK not initialized. Call initEkyc() first.");const o={"X-Session-Id":e,"X-Ekyc-Device-Info":`browser|${s}`};console.log("🎭 Ensuring FaceTec SDK is initialized...");const n=await I(o,!0);if(!n.faceTecInitialized)throw new Error("FaceTec SDK not properly initialized");const r={"X-Device-Key":s||n.data?.deviceKey,"X-Session-Id":e,"X-Ekyc-Token":n.data?.ekycToken||l.getEkycToken(),"X-Tid":d.getUniqueId(),correlationid:d.getUniqueId()},i={onComplete:(e,t)=>({sessionResult:e,networkResponseStatus:t})},c=new Promise(((e,t)=>{i.onComplete=(a,s)=>{u.isSuccess()||a&&a.status===FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully?e({sessionResult:a,networkResponseStatus:s}):t(new Error("Liveness check failed or was cancelled"))}}));console.log("🔍 Creating LivenessCheckProcessor...");const u=new(a(266))(n.data.sessionFaceTec,i,r["X-Device-Key"],r);console.log("⏳ Waiting for liveness check to complete...");const g=await c,p={success:u.isSuccess(),liveness:{sessionId:g.sessionResult?.sessionId||`liveness_${Date.now()}`,livenessScore:1,isLive:u.isSuccess(),confidence:u.isSuccess()?"high":"low",timestamp:(new Date).toISOString()},sessionId:e,deviceId:s,faceTecInitialized:!0,rawResult:g};return console.log("✅ Liveness check completed successfully:",p.success),t&&"function"==typeof t&&t({responseCode:p.success?"CUS-KYC-1000":"ERROR",description:p.success?"Liveness check successful":"Unable to Process",success:p.success,data:p}),p}catch(e){throw console.error("❌ Error performing liveness check:",e),e.message,t&&"function"==typeof t&&t({responseCode:"ERROR",responseDescription:e.message||"Failed to perform liveness check",success:!1,error:e.message}),e}}}},382:(e,t,a)=>{const s=new(a(706));e.exports={loadFaceTecSDK:()=>s.loadFaceTecSDK(),initializeFaceTec:(e,t)=>s.initializeFaceTec(e,t),getFaceTecVersion:()=>s.getFaceTecVersion()}},405:(e,t,a)=>{const s=a(719);e.exports=class{constructor(){this.baseUrl="/api"}async postIDScanFront(e,t={},a=null){const o=performance.now();return new Promise(((n,r)=>{const i=new XMLHttpRequest,c=`${this.baseUrl}/match-3d-2d-idscan/front`;s.logMessage(`Starting front ID scan request to: ${c}`),a&&"function"==typeof a&&i.upload.addEventListener("progress",(e=>{if(e.lengthComputable){const t=e.loaded/e.total*100;a(t),s.logData("Front scan upload progress",`${t.toFixed(1)}%`)}})),i.addEventListener("loadend",(()=>{const e=performance.now();if(i.readyState===XMLHttpRequest.DONE)try{if(i.status>=200&&i.status<300){const t=JSON.parse(i.responseText);s.logPerformance("IDScanFrontDataSource.postIDScanFront",o,e),s.logApiCall(c,"POST",`Success (${i.status})`),s.logData("Front API Response",{status:i.status,wasProcessed:t.wasProcessed,error:t.error,hasScanResultBlob:!!t.scanResultBlob,scanType:"front"}),n(t)}else{s.logPerformance("IDScanFrontDataSource.postIDScanFront (failed)",o,e),s.logError(`Front API call failed with status ${i.status}`);let t=null;try{t=JSON.parse(i.responseText)}catch(e){s.logError("Failed to parse error response",e)}r(new Error(`HTTP error! status: ${i.status}, message: ${t?.errorMessage||"Unknown error"}`))}}catch(t){s.logPerformance("IDScanFrontDataSource.postIDScanFront (parse error)",o,e),s.logError("Error parsing front scan response:",t),r(t)}})),i.addEventListener("error",(()=>{const e=performance.now();s.logPerformance("IDScanFrontDataSource.postIDScanFront (network error)",o,e),s.logError("Network error during front ID scan request"),r(new Error("Network error occurred during front ID scan"))})),i.addEventListener("timeout",(()=>{const e=performance.now();s.logPerformance("IDScanFrontDataSource.postIDScanFront (timeout)",o,e),s.logError("Front ID scan request timed out"),r(new Error("Front ID scan request timed out"))})),i.open("POST",c,!0),i.timeout=6e4,i.setRequestHeader("Content-Type","application/json"),i.setRequestHeader("Accept","application/json"),Object.keys(t).forEach((e=>{void 0!==t[e]&&null!==t[e]&&(i.setRequestHeader(e,t[e]),s.logData(`Front scan header: ${e}`,t[e]))}));const l=JSON.stringify(e);s.logData("Front scan request body size",`${l.length} bytes`),s.logData("Front scan data keys",Object.keys(e)),i.send(l)}))}validateFrontScanData(e){if(!e)throw new Error("Front scan data is required");if(!e.idScan)throw new Error("ID scan data is required for front scan");if(!e.idScanFrontImage)throw new Error("Front image is required for front ID scan");return s.logMessage("Front scan data validation passed"),!0}}},411:(e,t,a)=>{const s=a(599),o=a(641);e.exports={UuidGenerator:s,TokenStorage:o}},453:(e,t,a)=>{const s=a(0),o=a(535),n=a(715),r=a(307),i=a(719),c=a(345);var l=l=function(e,t,a,l){var d=this;this.latestNetworkRequest=new XMLHttpRequest,this.deviceKey=a||null,this.additionalHeaders=l||{},this.postIDScanOnlyUseCase=new s,this.postIDScanFrontUseCase=new o,this.postIDScanBackUseCase=new n,this.postLivenessCheckUseCase=new r,this.processSessionResultWhileFaceTecSDKWaits=function(e,t){if(d.latestSessionResult=e,e.status!==FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully)return d.latestNetworkRequest.abort(),d.latestNetworkRequest=new XMLHttpRequest,void t.cancel();d.executeLivenessCheckUseCase(e,t)},this.executeLivenessCheckUseCase=async function(e,t){try{const a=function(e){t.uploadProgress(e)},s=await d.postLivenessCheckUseCase.execute({sessionResult:e,deviceKey:d.deviceKey,additionalHeaders:d.additionalHeaders,onProgress:a});s.success?(FaceTecSDK.FaceTecCustomization.setOverrideResultScreenSuccessMessage("Face Scanned\n3D Liveness Proven"),t.proceedToNextStep(s.scanResultBlob)):d.cancelDueToNetworkError(s.errorMessage||"Liveness check failed",t)}catch(e){console.error("Liveness check use case error:",e),d.cancelDueToNetworkError("Network error during liveness check",t)}},this.processIDScanResultWhileFaceTecSDKWaits=function(e,t){if(d.latestIDScanResult=e,e.status!==FaceTecSDK.FaceTecIDScanStatus.Success)return d.latestNetworkRequest.abort(),d.latestNetworkRequest=new XMLHttpRequest,void t.cancel();d.executeIDScanUseCase(e,t)},this.executeIDScanUseCase=async function(e,t){try{const a=function(e){t.uploadProgress(e)};let s,o="unknown";if(e.backImages&&e.backImages[0]?(o="back",i.logMessage("Routing to back ID scan endpoint"),s=await d.postIDScanBackUseCase.execute({idScanResult:e,deviceKey:d.deviceKey,additionalHeaders:d.additionalHeaders,onProgress:a})):e.frontImages&&e.frontImages[0]&&(o="front",i.logMessage("Routing to front ID scan endpoint"),s=await d.postIDScanFrontUseCase.execute({idScanResult:e,deviceKey:d.deviceKey,additionalHeaders:d.additionalHeaders,onProgress:a})),i.logData("ID scan completed",{scanType:o,success:s.success}),s.success)if(s.allowRetry)i.logMessage(`${o} scan: Retry allowed - ${s.retryReason}`),d.configureRetryMessages(o),t.proceedToNextStep(s.scanResultBlob);else{if(d.ocrResultsCallback){const e=d.extractOcrValueForCallback(s);d.callbackResult={success:!0,description:`${o} ID scan completed successfully`,userOcrValue:e,userConfirmedValue:null,dopaResult:null,timestamp:(new Date).toISOString()},i.logMessage("Stored callback result for success case"),i.logData("Success Callback Result",d.callbackResult)}"back"===o&&s.userOcrValue&&d.showSuccessOverlay(s.userOcrValue),d.configureSuccessMessages(o),t.proceedToNextStep(s.scanResultBlob)}else{if(d.ocrResultsCallback&&!s.allowRetry){const e=d.extractOcrValueForCallback(s);d.callbackResult={success:!1,description:s.errorMessage||`${o} ID scan failed`,userOcrValue:e,userConfirmedValue:null,dopaResult:null,timestamp:(new Date).toISOString()},i.logMessage("Stored callback result for error case"),i.logData("Error Callback Result",d.callbackResult)}d.cancelDueToNetworkError(s.errorMessage||`${o} ID scan failed`,t)}}catch(e){console.error("ID scan use case error:",e),d.ocrResultsCallback&&(d.callbackResult={success:!1,description:"Network error during ID scan: "+e.message,userOcrValue:null,userConfirmedValue:null,dopaResult:null,timestamp:(new Date).toISOString()},i.logMessage("Stored callback result for exception case"),i.logData("Exception Callback Result",d.callbackResult)),d.cancelDueToNetworkError("Network error during ID scan",t)}},this.configureSuccessMessages=function(e){"front"===e?FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Front Scan Complete","Front of ID<br/>Scanned","Front Scan Complete","Passport Scan Complete","Photo ID Front<br/>Complete","ID Photo Capture<br/>Complete","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again"):"back"===e?FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Back Scan Complete","Back of ID<br/>Scanned","ID Scan Complete","Passport Scan Complete","Photo ID Back<br/>Complete","ID Photo Capture<br/>Complete","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again"):FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Front Scan Complete","Front of ID<br/>Scanned","ID Scan Complete","Passport Scan Complete","Photo ID Scan<br/>Complete","ID Photo Capture<br/>Complete","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again")},this.configureRetryMessages=function(e){i.logMessage(`Configuring retry messages for ${e} scan (CUS-KYC-7102)`),"front"===e?FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Front Scan Complete<br/>Please Try Again","Front of ID Scanned<br/>Please Try Again","Front Scan Complete<br/>Please Try Again","Passport Scan Complete<br/>Please Try Again","Photo ID Front Complete<br/>Please Try Again","ID Photo Capture Complete<br/>Please Try Again","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again"):"back"===e?FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Back Scan Complete<br/>Please Try Again","Back of ID Scanned<br/>Please Try Again","ID Scan Complete<br/>Please Try Again","Passport Scan Complete<br/>Please Try Again","Photo ID Back Complete<br/>Please Try Again","ID Photo Capture Complete<br/>Please Try Again","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again"):FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Scan Complete<br/>Please Try Again","ID Scanned<br/>Please Try Again","ID Scan Complete<br/>Please Try Again","Passport Scan Complete<br/>Please Try Again","Photo ID Scan Complete<br/>Please Try Again","ID Photo Capture Complete<br/>Please Try Again","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again")},this.showSuccessOverlay=function(e=null){let t=null;e&&(t=c.validateAll(e),i.logData("OCR Validation Result",t));const a=document.createElement("div");a.id="ekyc-success-overlay",a.style.cssText="\n                position: fixed;\n                top: 0;\n                left: 0;\n                width: 100%;\n                height: 100%;\n                background: rgba(0, 0, 0, 0.9);\n                display: flex;\n                justify-content: center;\n                align-items: center;\n                z-index: 10000;\n                font-family: Arial, sans-serif;\n                overflow-y: auto;\n                padding: 20px;\n                box-sizing: border-box;\n            ";const s=e?d.generateOcrFieldsHtml(e,t):"";a.innerHTML=`\n                <div style="\n                    background: white;\n                    border-radius: 12px;\n                    max-width: 600px;\n                    width: 100%;\n                    max-height: 90vh;\n                    overflow-y: auto;\n                    box-shadow: 0 8px 32px rgba(0,0,0,0.3);\n                ">\n                    \x3c!-- Header --\x3e\n                    <div style="\n                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n                        color: white;\n                        padding: 24px;\n                        border-radius: 12px 12px 0 0;\n                        text-align: center;\n                        position: relative;\n                    ">\n                        <h2 style="margin: 0; font-size: 24px; font-weight: 600;">Review & Confirm</h2>\n                        <p style="margin: 8px 0 0 0; opacity: 0.9; font-size: 14px;">Information</p>\n                        <button onclick="document.getElementById('ekyc-success-overlay').remove()"\n                                style="\n                                    position: absolute;\n                                    top: 16px;\n                                    right: 16px;\n                                    background: rgba(255,255,255,0.2);\n                                    border: none;\n                                    color: white;\n                                    width: 32px;\n                                    height: 32px;\n                                    border-radius: 50%;\n                                    cursor: pointer;\n                                    font-size: 18px;\n                                    display: flex;\n                                    align-items: center;\n                                    justify-content: center;\n                                ">×</button>\n                    </div>\n\n                    \x3c!-- Content --\x3e\n                    <div style="padding: 24px;">\n                        ${s||'<div style="text-align: center; color: #666; padding: 40px;"><div style="font-size: 48px; margin-bottom: 16px;">✓</div><h3 style="margin: 0 0 8px 0;">ID Card Scan Complete</h3><p style="margin: 0; opacity: 0.7;">Back side of ID card successfully scanned</p></div>'}\n\n                        \x3c!-- Action Buttons --\x3e\n                        <div style="margin-top: 32px; text-align: center;">\n                            <button id="ekyc-confirm-button"\n                                    style="\n                                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n                                        color: white;\n                                        border: none;\n                                        padding: 12px 32px;\n                                        border-radius: 8px;\n                                        font-size: 16px;\n                                        font-weight: 600;\n                                        cursor: pointer;\n                                        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n                                        transition: transform 0.2s;\n                                    "\n                                    onmouseover="this.style.transform='translateY(-2px)'"\n                                    onmouseout="this.style.transform='translateY(0)'">\n                                Confirm Information\n                            </button>\n                        </div>\n\n                        ${t&&t.errors.length>0?'\n                        <div style="margin-top: 16px; padding: 12px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px;">\n                            <p style="margin: 0; color: #856404; font-size: 14px; font-weight: 600;">Please check your information before submitting</p>\n                        </div>\n                        ':""}\n                    </div>\n                </div>\n            `,document.body.appendChild(a);const o=document.getElementById("ekyc-confirm-button");o&&e&&o.addEventListener("click",(function(){d.handleUserConfirmation(e)})),d._overlayShownAndCancelled&&d._overlayShownAndCancelled(),faceTecIdScanResultCallback.cancel(),setTimeout((()=>{document.getElementById("ekyc-success-overlay")&&document.getElementById("ekyc-success-overlay").remove()}),3e4)},this.generateOcrFieldsHtml=function(e,t){let a="";return[{key:"nationalId",label:"National ID Number",required:!0},{key:"titleTh",label:"Title (TH)",required:!0},{key:"firstNameTh",label:"First Name (TH)",required:!0},{key:"middleNameTh",label:"Middle Name (TH)",required:!1,note:"(if applicable)"},{key:"lastNameTh",label:"Last Name (TH)",required:!0},{key:"titleEn",label:"Title (ENG)",required:!0},{key:"firstNameEn",label:"First Name (ENG)",required:!0},{key:"middleNameEn",label:"Middle Name (ENG)",required:!1,note:"(if applicable)"},{key:"lastNameEn",label:"Last Name (ENG)",required:!0},{key:"dateOfBirth",label:"Birth Date",required:!0,note:"(A.D. i.e. 2022)",isDate:!0},{key:"dateOfIssue",label:"Issue Date",required:!0,note:"(A.D. i.e. 2022)",isDate:!0},{key:"dateOfExpiry",label:"Expiry Date",required:!0,note:"(A.D. i.e. 2022)",isDate:!0},{key:"laserId",label:"Laser ID",required:!0}].forEach((s=>{const o=e[s.key]||"",n=t?.fieldValidations[s.key],r=n?.status||"unknown",i=c.getStatusIcon(r),l=c.getStatusColor(r);let d=o,u=null;s.isDate&&o&&(u=o.split(/[\/\-\.]/),3===u.length&&(d=`${u[0]} / ${u[1]} / ${u[2]}`)),a+=`\n                    <div style="margin-bottom: 16px;">\n                        <label style="\n                            display: block;\n                            font-size: 14px;\n                            font-weight: 600;\n                            color: #333;\n                            margin-bottom: 6px;\n                        ">\n                            ${s.label}${s.required?"*":""}\n                            ${s.note?`<span style="font-weight: 400; color: #666; font-size: 12px;"> ${s.note}</span>`:""}\n                            <span style="color: ${l}; margin-left: 8px; font-size: 16px;">${i}</span>\n                        </label>\n                        ${s.isDate?`\n                            <div style="display: flex; gap: 8px; align-items: center;">\n                                <input type="text" value="${u&&u[0]?u[0]:""}" placeholder="DD"\n                                       style="width: 60px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; text-align: center;" readonly />\n                                <span style="color: #666;">/</span>\n                                <input type="text" value="${u&&u[1]?u[1]:""}" placeholder="MM"\n                                       style="width: 60px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; text-align: center;" readonly />\n                                <span style="color: #666;">/</span>\n                                <input type="text" value="${u&&u[2]?u[2]:""}" placeholder="YYYY"\n                                       style="width: 80px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; text-align: center;" readonly />\n                            </div>\n                        `:`\n                            <input type="text" value="${d}"\n                                   style="\n                                       width: 100%;\n                                       padding: 12px;\n                                       border: 1px solid ${"invalid"===r||"missing"===r?"#f44336":"#ddd"};\n                                       border-radius: 6px;\n                                       font-size: 14px;\n                                       background: ${"missing"===r?"#fff3e0":"#f9f9f9"};\n                                       color: #333;\n                                   " readonly />\n                        `}\n                        ${n&&!n.isValid&&n.error?`\n                            <div style="margin-top: 4px; color: #f44336; font-size: 12px;">\n                                ${n.error}\n                            </div>\n                        `:""}\n                    </div>\n                `})),a},this.handleUserConfirmation=function(e){if(i.logMessage("User confirmed information from overlay"),i.logData("User Confirmed OCR Value",e),d.ocrResultsCallback){const t=d.extractOcrValueForCallback({userOcrValue:e});d.callbackResult={success:!0,description:"User confirmed information",userOcrValue:t,userConfirmedValue:e,dopaResult:null,timestamp:(new Date).toISOString()},i.logMessage("Stored callback result for user confirmation"),i.logData("User Confirmation Callback Result",d.callbackResult)}const t=document.getElementById("ekyc-success-overlay");t&&t.remove()},this.getCallbackResult=function(){return d.callbackResult||null},this.executeStoredCallback=function(){return!(!d.ocrResultsCallback||!d.callbackResult||(i.logMessage("Executing stored OCR results callback"),i.logData("Callback Result",d.callbackResult),d.ocrResultsCallback(d.callbackResult.success,d.callbackResult.description,d.callbackResult.userOcrValue,d.callbackResult.userConfirmedValue,d.callbackResult.dopaResult),0))},this.extractOcrValueForCallback=function(e){return i.logMessage("Extracting OCR value for callback"),i.logData("Input result for OCR extraction",e),e&&e.userOcrValue?(i.logMessage("Using existing userOcrValue from result"),e.userOcrValue):e&&e.ocrData?(i.logMessage("Extracting OCR data from result.ocrData"),d.extractOcrFromApiResponse(e.ocrData)):e&&e.originalResponse&&e.originalResponse.data?(i.logMessage("Extracting OCR data from API response structure"),d.extractOcrFromApiResponse(e.originalResponse.data)):(i.logMessage("No OCR data found in result, returning null"),null)},this.extractOcrFromApiResponse=function(e){try{i.logMessage("Extracting OCR data from API response"),i.logData("API Data",e);const t={nationalId:null,titleTh:null,firstNameTh:null,middleNameTh:null,lastNameTh:null,titleEn:null,firstNameEn:null,middleNameEn:null,lastNameEn:null,dateOfBirth:null,dateOfIssue:null,dateOfExpiry:null,laserId:null};if(e.documentData){let a;a="string"==typeof e.documentData?JSON.parse(e.documentData):e.documentData,a.scannedValues&&a.scannedValues.groups&&a.scannedValues.groups.forEach((e=>{e.fields&&e.fields.forEach((e=>{const a=e.fieldKey,s=e.value||"";switch(a){case"nationalId":case"idNumber":t.nationalId=s;break;case"fullname":const e=s.split(" ").filter((e=>e.trim()));e.length>=2&&(t.titleTh=e[0],t.firstNameTh=e[1],e.length>2&&(t.lastNameTh=e.slice(2).join(" ")));break;case"firstName":const a=s.split(" ").filter((e=>e.trim()));a.length>=1&&(t.titleEn=a[0],a.length>1&&(t.firstNameEn=a.slice(1).join(" ")));break;case"lastName":case"lastNameEn":t.lastNameEn=s;break;case"dateOfBirth":t.dateOfBirth=d.convertThaiDateToStandard(s);break;case"dateOfIssue":t.dateOfIssue=d.convertThaiDateToStandard(s);break;case"dateOfExpiry":t.dateOfExpiry=d.convertThaiDateToStandard(s);break;case"laserId":t.laserId=s}}))}))}return i.logMessage("OCR extraction completed"),i.logData("Extracted OCR Value",t),t}catch(e){return i.logMessage("Error extracting OCR data: "+e.message),console.error("Error extracting OCR data:",e),null}},this.convertThaiDateToStandard=function(e){if(!e)return null;try{const t={"ม.ค.":"01","ก.พ.":"02","มี.ค.":"03","เม.ย.":"04","พ.ค.":"05","มิ.ย.":"06","ก.ค.":"07","ส.ค.":"08","ก.ย.":"09","ต.ค.":"10","พ.ย.":"11","ธ.ค.":"12"},a=e.match(/(\d{1,2})\s+([ก-ฮ\.]+)\s+(\d{4})/);if(a){const e=a[1].padStart(2,"0"),s=a[2],o=a[3],n=t[s];if(n)return`${e}/${n}/${o}`}return e}catch(t){return console.error("Error converting Thai date:",t),e}},this.onFaceTecSDKCompletelyDone=function(){null!=d.latestIDScanResult&&(d.success=d.latestIDScanResult.isCompletelyDone),d.sampleAppControllerReference.onComplete(d.latestSessionResult,d.latestIDScanResult,d.latestNetworkRequest.status)},this.cancelDueToNetworkError=function(e,t){!1===d.cancelledDueToNetworkError&&(console.error(e),d.cancelledDueToNetworkError=!0,t.cancel())},this.isSuccess=function(){return d.success},this.success=!1,this.sampleAppControllerReference=t,this.latestSessionResult=null,this.latestIDScanResult=null,this.cancelledDueToNetworkError=!1,FaceTecSDK.FaceTecCustomization.setIDScanUploadMessageOverrides("Uploading<br/>Encrypted<br/>ID Scan","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>ID Scan","Uploading<br/>Encrypted<br/>Back of ID","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>Back of ID","Uploading<br/>Your Confirmed Info","Still Uploading...<br/>Slow Connection","Info Saved","Processing"),new FaceTecSDK.FaceTecSession(this,e)};e.exports=l},489:(e,t,a)=>{const s=a(719);e.exports=class{constructor(){this.baseUrl="/api"}async postIDScanBack(e,t={},a=null){const o=performance.now();return new Promise(((n,r)=>{const i=new XMLHttpRequest,c=`${this.baseUrl}/match-3d-2d-idscan/back`;s.logMessage(`Starting back ID scan request to: ${c}`),a&&"function"==typeof a&&i.upload.addEventListener("progress",(e=>{if(e.lengthComputable){const t=e.loaded/e.total*100;a(t),s.logData("Back scan upload progress",`${t.toFixed(1)}%`)}})),i.addEventListener("loadend",(()=>{const e=performance.now();if(i.readyState===XMLHttpRequest.DONE)try{if(i.status>=200&&i.status<300){const t=JSON.parse(i.responseText);s.logPerformance("IDScanBackDataSource.postIDScanBack",o,e),s.logApiCall(c,"POST",`Success (${i.status})`),s.logData("Back API Response",{status:i.status,wasProcessed:t.wasProcessed,error:t.error,hasScanResultBlob:!!t.scanResultBlob,hasOcrData:!!t.originalResponse?.data?.documentData,scanType:"back"}),n(t)}else{s.logPerformance("IDScanBackDataSource.postIDScanBack (failed)",o,e),s.logError(`Back API call failed with status ${i.status}`);let t=null;try{t=JSON.parse(i.responseText)}catch(e){s.logError("Failed to parse error response",e)}r(new Error(`HTTP error! status: ${i.status}, message: ${t?.errorMessage||"Unknown error"}`))}}catch(t){s.logPerformance("IDScanBackDataSource.postIDScanBack (parse error)",o,e),s.logError("Error parsing back scan response:",t),r(t)}})),i.addEventListener("error",(()=>{const e=performance.now();s.logPerformance("IDScanBackDataSource.postIDScanBack (network error)",o,e),s.logError("Network error during back ID scan request"),r(new Error("Network error occurred during back ID scan"))})),i.addEventListener("timeout",(()=>{const e=performance.now();s.logPerformance("IDScanBackDataSource.postIDScanBack (timeout)",o,e),s.logError("Back ID scan request timed out"),r(new Error("Back ID scan request timed out"))})),i.open("POST",c,!0),i.timeout=6e4,i.setRequestHeader("Content-Type","application/json"),i.setRequestHeader("Accept","application/json"),Object.keys(t).forEach((e=>{void 0!==t[e]&&null!==t[e]&&(i.setRequestHeader(e,t[e]),s.logData(`Back scan header: ${e}`,t[e]))}));const l=JSON.stringify(e);s.logData("Back scan request body size",`${l.length} bytes`),s.logData("Back scan data keys",Object.keys(e)),i.send(l)}))}validateBackScanData(e){if(!e)throw new Error("Back scan data is required");if(!e.idScan)throw new Error("ID scan data is required for back scan");if(!e.idScanBackImage)throw new Error("Back image is required for back ID scan");return s.logMessage("Back scan data validation passed"),!0}}},518:(e,t,a)=>{const s=a(719);e.exports=class{constructor(){this.baseUrl="/api"}async postIDScanOnly(e,t={},a=null){return new Promise(((o,n)=>{const r=performance.now();try{const i=`${this.baseUrl}/idscan-only`;s.logApiCall(i,"POST","Starting request");const c=new XMLHttpRequest;a&&"function"==typeof a&&(c.upload.onprogress=function(e){if(e.lengthComputable){const t=e.loaded/e.total;s.logIDScanProgress("Uploading",t),a(t)}}),c.onreadystatechange=function(){if(c.readyState===XMLHttpRequest.DONE){const e=performance.now();try{if(c.status>=200&&c.status<300){const t=JSON.parse(c.responseText);s.logPerformance("IDScanDataSource.postIDScanOnly",r,e),s.logApiCall(i,"POST",`Success (${c.status})`),s.logData("API Response",{status:c.status,wasProcessed:t.wasProcessed,error:t.error,hasScanResultBlob:!!t.scanResultBlob}),o(t)}else s.logPerformance("IDScanDataSource.postIDScanOnly (failed)",r,e),s.logError(`API call failed with status ${c.status}`),n(new Error(`HTTP error! status: ${c.status}`))}catch(e){s.logError("Failed to parse API response",e),n(new Error("Failed to parse response JSON"))}}},c.onerror=function(){const e=performance.now();s.logPerformance("IDScanDataSource.postIDScanOnly (network error)",r,e),s.logError("Network request failed"),n(new Error("Network request failed"))},c.open("POST",i),c.setRequestHeader("Content-Type","application/json"),Object.keys(t).forEach((e=>{void 0!==t[e]&&c.setRequestHeader(e,t[e])}));const l=JSON.stringify(e);s.logMessage(`Sending request to ${i} with ${Object.keys(e).length} data fields`),c.send(l)}catch(e){s.logError("IDScanDataSource - postIDScanOnly error",e),n(e)}}))}}},535:(e,t,a)=>{const s=a(216),o=a(599),n=a(719);e.exports=class{constructor(){this.faceTecRepository=new s}async execute({idScanResult:e,deviceKey:t,additionalHeaders:a={},onProgress:s=null}){const o=performance.now();try{n.logMessage("Starting PostIDScanFrontUseCase execution"),n.logMessage("Preparing front scan data...");const r=this.prepareFrontScanData(e);n.logData("Front Scan Data Keys",Object.keys(r)),n.logMessage("Preparing headers...");const i=this.prepareHeaders(e,t,a);n.logData("Request Headers",Object.keys(i)),n.logMessage("Validating front scan data..."),this.faceTecRepository.validateFrontScanData(r),n.logSuccess("Front scan data validation passed"),n.logMessage("Submitting front scan to repository...");const c=await this.faceTecRepository.submitIDScanFront(r,i,s);n.logMessage("Processing front scan response...");const l=this.processResponse(c),d=performance.now();return n.logPerformance("PostIDScanFrontUseCase.execute",o,d),n.logSuccess(`Front UseCase completed successfully: ${l.success}`),l}catch(e){const t=performance.now();throw n.logPerformance("PostIDScanFrontUseCase.execute (failed)",o,t),n.logError("PostIDScanFrontUseCase execution failed:",e),e}}prepareFrontScanData(e){const t={idScan:e.idScan,enableConfirmInfo:!0};if(!e.frontImages||!e.frontImages[0])throw new Error("Front image is required for front ID scan processing");return t.idScanFrontImage=e.frontImages[0],t}prepareHeaders(e,t,a){const s={...a};return t&&(s["X-Device-Key"]=t),e.sessionId&&"undefined"!=typeof FaceTecSDK&&(s["X-User-Agent"]=FaceTecSDK.createFaceTecAPIUserAgentString(e.sessionId)),s["X-Tid"]?n.logData("X-Tid header already present for front scan",s["X-Tid"]):(s["X-Tid"]=o.getUniqueId(),n.logData("Generated X-Tid header for front scan",s["X-Tid"])),s["Content-Type"]="application/json",s.Accept="application/json",Object.keys(s).forEach((e=>{void 0===s[e]&&delete s[e]})),n.logData("Front scan prepared headers",Object.keys(s)),s}processResponse(e){const t=this.shouldAllowRetryForResponse(e);let a=null;if(e.originalResponse&&e.originalResponse.data&&e.originalResponse.data.documentData)try{const t=JSON.parse(e.originalResponse.data.documentData);n.logData("Parsed front scan documentData",t),a=this.extractFrontOcrDataFromDocumentData(t),a?n.logData("Extracted Front OCR Data",Object.keys(a)):n.logMessage("No valid front OCR fields found in documentData")}catch(e){n.logError("Failed to parse front scan documentData JSON:",e),n.logMessage("No front OCR data found in response")}else n.logMessage("No documentData found in front scan response");return{success:!0===e.wasProcessed&&!1===e.error||t,scanResultBlob:e.scanResultBlob||e.originalResponse?.data?.scanResultBlob,originalResponse:e.originalResponse,errorMessage:e.errorMessage,userOcrValue:a,scanType:"front",allowRetry:t,retryReason:t?"CUS-KYC-7102: OCR Match Photo ID and Selfie Fail - Retry Allowed":null}}shouldAllowRetryForResponse(e){return!(!e.originalResponse||"CUS-KYC-7102"!==e.originalResponse.code||(n.logMessage("Front scan: CUS-KYC-7102 detected - allowing retry with scanResultBlob"),e.scanResultBlob||e.originalResponse?.data?.scanResultBlob?(n.logData("Front scan: ScanResultBlob available for retry","Yes"),0):(n.logError("Front scan: CUS-KYC-7102 detected but no scanResultBlob available for retry"),1)))}extractFrontOcrDataFromDocumentData(e){if(!e||!e.scannedValues||!e.scannedValues.groups)return n.logMessage("Invalid front scan documentData structure"),null;const t={nationalId:null,scanType:"front",scanQuality:null},a={idNumber:"nationalId",quality:"scanQuality"};return e.scannedValues.groups.forEach((e=>{e.fields&&Array.isArray(e.fields)&&e.fields.forEach((e=>{if(e.fieldKey&&e.value&&a[e.fieldKey]){const s=a[e.fieldKey];t[s]=e.value,n.logData(`Front scan mapped ${e.fieldKey} -> ${s}`,e.value)}}))})),Object.values(t).some((e=>null!==e))?t:(n.logMessage("No valid front OCR fields found in documentData structure"),null)}}},548:(e,t,a)=>{const s=a(599),{TokenStorage:o}=a(411);e.exports=class{async getSessionToken(e={}){try{const t="/api/session-token",a=e["X-Ekyc-Device-Info"]?null:s.getDeviceId(),n=s.getUniqueId(),r=e["X-Session-Id"]||s.getUniqueId();e["X-Session-Id"]||o.storeSessionId(r);const i={"Content-Type":"application/json",Accept:"application/json","X-Ekyc-Sdk-Version":"1.0.0","X-Ekyc-Device-Info":`browser|${a}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`,"X-Session-Id":`${r}`,"X-Tid":`${n}`,correlationid:`${s.getUniqueId()}`,...e};e.Authorization&&(i.Authorization=e.Authorization);const c=await fetch(t,{method:"GET",headers:i});if(!c.ok)throw new Error(`API request failed with status ${c.status}`);return await c.json()}catch(e){throw console.error("Error getting session token:",e),e}}async getFaceTecSessionToken(e={}){try{const t="/api/facetec-session-token",a=e["X-Ekyc-Device-Info"]?null:s.getDeviceId(),n=s.getUniqueId(),r=o.getSessionId(),i={"Content-Type":"application/json",Accept:"application/json","X-Ekyc-Sdk-Version":"1.0.0","X-Ekyc-Device-Info":`browser|${a}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`,"X-Session-Id":`${e["X-Session-Id"]||r||s.getUniqueId()}`,"X-Tid":`${n}`,correlationid:`${s.getUniqueId()}`,...e};e.Authorization&&(i.Authorization=e.Authorization);const c=await fetch(t,{method:"GET",headers:i});if(!c.ok)throw new Error(`API request failed with status ${c.status}`);return await c.json()}catch(e){throw console.error("Error getting FaceTec session token:",e),e}}async getFaceTecSessionTokenWithEkycToken(e={}){try{const t="/api/facetec-session-token",a=o.getEkycToken();if(!a)throw new Error("No eKYC token found. Please get a session token first.");const n=e["X-Ekyc-Device-Info"]?null:s.getDeviceId(),r=e["X-Tid"]||s.getUniqueId(),i=o.getSessionId(),c=e["X-Session-Id"]||i||s.getUniqueId(),l=e.correlationid||s.getUniqueId(),d={"Content-Type":"application/json",Accept:"application/json","X-Ekyc-Sdk-Version":e["X-Ekyc-Sdk-Version"]||"1.0.0","X-Ekyc-Device-Info":e["X-Ekyc-Device-Info"]||`browser|${n}`,"X-Session-Id":`${c}`,"X-Tid":`${r}`,correlationid:`${l}`,"X-Ekyc-Token":a,...e};e.Authorization&&(d.Authorization=e.Authorization);const u=await fetch(t,{method:"GET",headers:d});if(!u.ok)throw new Error(`API request failed with status ${u.status}`);return await u.json()}catch(e){throw console.error("Error getting FaceTec session token with eKYC token:",e),e}}}},592:(e,t,a)=>{const s=a(189);e.exports=class{execute(e,t="USD"){return new s(e,t).format()}}},599:e=>{e.exports=class{static generateUuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}static getDeviceId(){if("undefined"!=typeof window&&window.localStorage){let e=localStorage.getItem("ekyc_device_id");return e||(e=this.generateUuid(),localStorage.setItem("ekyc_device_id",e)),e}return this.generateUuid()}static getUniqueId(){return this.generateUuid()}}},641:e=>{e.exports=class{static storeToken(e,t){if("undefined"!=typeof window&&window.localStorage&&t)try{return localStorage.setItem(e,t),!0}catch(e){return console.error("Error storing token:",e),!1}return!1}static getToken(e){return"undefined"!=typeof window&&window.localStorage?localStorage.getItem(e):null}static removeToken(e){if("undefined"!=typeof window&&window.localStorage)try{return localStorage.removeItem(e),!0}catch(e){return console.error("Error removing token:",e),!1}return!1}static storeEkycToken(e){return this.storeToken("ekyc_token",e)}static getEkycToken(){return this.getToken("ekyc_token")}static removeEkycToken(){return this.removeToken("ekyc_token")}static storeSessionId(e){return this.storeToken("ekyc_session_id",e)}static getSessionId(){return this.getToken("ekyc_session_id")}static removeSessionId(){return this.removeToken("ekyc_session_id")}}},706:e=>{e.exports=class{loadFaceTecSDK(){return new Promise(((e,t)=>{if("undefined"!=typeof window&&window.FaceTecSDK)return void e(window.FaceTecSDK);const a=document.createElement("script");a.src="/core-sdk/FaceTecSDK.js/FaceTecSDK.js",a.async=!0,a.onload=()=>{window.FaceTecSDK?e(window.FaceTecSDK):t(new Error("FaceTecSDK not found after loading script"))},a.onerror=()=>{t(new Error("Failed to load FaceTecSDK script"))},document.head.appendChild(a)}))}async initializeFaceTec(e,t){try{const a=await this.loadFaceTecSDK();return a.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources"),a.setImagesDirectory("/core-sdk/FaceTec_images"),new Promise(((s,o)=>{a.initializeInDevelopmentMode(e,t,(e=>{e?(console.log("FaceTecSDK initialized successfully"),s(!0)):(console.error("FaceTecSDK failed to initialize"),o(new Error("FaceTecSDK failed to initialize")))}))}))}catch(e){throw console.error("Error loading FaceTecSDK:",e),e}}async getFaceTecVersion(){try{return(await this.loadFaceTecSDK()).version()}catch(e){throw console.error("Error getting FaceTecSDK version:",e),e}}}},715:(e,t,a)=>{const s=a(216),o=a(599),n=a(719);e.exports=class{constructor(){this.faceTecRepository=new s}async execute({idScanResult:e,deviceKey:t,additionalHeaders:a={},onProgress:s=null}){const o=performance.now();try{n.logMessage("Starting PostIDScanBackUseCase execution"),n.logMessage("Preparing back scan data...");const r=this.prepareBackScanData(e);n.logData("Back Scan Data Keys",Object.keys(r)),n.logMessage("Preparing headers...");const i=this.prepareHeaders(e,t,a);n.logData("Request Headers",Object.keys(i)),n.logMessage("Validating back scan data..."),this.faceTecRepository.validateBackScanData(r),n.logSuccess("Back scan data validation passed"),n.logMessage("Submitting back scan to repository...");const c=await this.faceTecRepository.submitIDScanBack(r,i,s);n.logMessage("Processing back scan response...");const l=this.processResponse(c),d=performance.now();return n.logPerformance("PostIDScanBackUseCase.execute",o,d),n.logSuccess(`Back UseCase completed successfully: ${l.success}`),l}catch(e){const t=performance.now();throw n.logPerformance("PostIDScanBackUseCase.execute (failed)",o,t),n.logError("PostIDScanBackUseCase execution failed:",e),e}}prepareBackScanData(e){const t={idScan:e.idScan,enableConfirmInfo:!0};if(!e.backImages||!e.backImages[0])throw new Error("Back image is required for back ID scan processing");return t.idScanBackImage=e.backImages[0],e.frontImages&&e.frontImages[0]&&(t.idScanFrontImage=e.frontImages[0]),t}prepareHeaders(e,t,a){const s={...a};return t&&(s["X-Device-Key"]=t),e.sessionId&&"undefined"!=typeof FaceTecSDK&&(s["X-User-Agent"]=FaceTecSDK.createFaceTecAPIUserAgentString(e.sessionId)),s["X-Tid"]?n.logData("X-Tid header already present for back scan",s["X-Tid"]):(s["X-Tid"]=o.getUniqueId(),n.logData("Generated X-Tid header for back scan",s["X-Tid"])),s["Content-Type"]="application/json",s.Accept="application/json",Object.keys(s).forEach((e=>{void 0===s[e]&&delete s[e]})),n.logData("Back scan prepared headers",Object.keys(s)),s}processResponse(e){const t=this.shouldAllowRetryForResponse(e);let a=null;if(e.originalResponse&&e.originalResponse.data&&e.originalResponse.data.documentData)try{const t=JSON.parse(e.originalResponse.data.documentData);n.logData("Parsed back scan documentData",t),a=this.extractBackOcrDataFromDocumentData(t),a?n.logData("Extracted Back OCR Data",Object.keys(a)):n.logMessage("No valid back OCR fields found in documentData")}catch(e){n.logError("Failed to parse back scan documentData JSON:",e),n.logMessage("No back OCR data found in response")}else n.logMessage("No documentData found in back scan response");return{success:!0===e.wasProcessed&&!1===e.error||t,scanResultBlob:e.scanResultBlob||e.originalResponse?.data?.scanResultBlob,originalResponse:e.originalResponse,errorMessage:e.errorMessage,userOcrValue:a,scanType:"back",allowRetry:t,retryReason:t?"CUS-KYC-7102: OCR Match Photo ID and Selfie Fail - Retry Allowed":null}}shouldAllowRetryForResponse(e){return!(!e.originalResponse||"CUS-KYC-7102"!==e.originalResponse.code||(n.logMessage("Back scan: CUS-KYC-7102 detected - allowing retry with scanResultBlob"),e.scanResultBlob||e.originalResponse?.data?.scanResultBlob?(n.logData("Back scan: ScanResultBlob available for retry","Yes"),0):(n.logError("Back scan: CUS-KYC-7102 detected but no scanResultBlob available for retry"),1)))}extractBackOcrDataFromDocumentData(e){if(!e||!e.scannedValues||!e.scannedValues.groups)return n.logMessage("Invalid back scan documentData structure"),null;const t={nationalId:null,titleTh:null,firstNameTh:null,middleNameTh:null,lastNameTh:null,titleEn:null,firstNameEn:null,middleNameEn:null,lastNameEn:null,dateOfBirth:null,dateOfIssue:null,dateOfExpiry:null,laserId:null},a={idNumber:"nationalId",firstName:"_rawFirstName",fullname:"_rawFullname",fullName:"_rawFullname",lastName:"lastNameEn",dateOfBirth:"dateOfBirth",dateOfIssue:"dateOfIssue",dateOfExpiration:"dateOfExpiry",customField1:"laserId"},s={_rawFirstName:null,_rawFullname:null};return e.scannedValues.groups.forEach((e=>{e.fields&&Array.isArray(e.fields)&&e.fields.forEach((e=>{if(e.fieldKey&&e.value&&a[e.fieldKey]){const o=a[e.fieldKey];o.startsWith("_raw")?(s[o]=e.value,n.logData(`Stored raw ${e.fieldKey}`,e.value)):(t[o]=e.value,n.logData(`Mapped ${e.fieldKey} -> ${o}`,e.value))}}))})),s._rawFullname&&this.parseThaiNameFields(s._rawFullname,t),s._rawFirstName&&this.parseEnglishNameFields(s._rawFirstName,t),this.convertDateFields(t),this.addFieldEditabilityMetadata(t),Object.values(t).some((e=>null!==e))?t:(n.logMessage("No valid back OCR fields found in documentData structure"),null)}parseThaiNameFields(e,t){if(!e||"string"!=typeof e)return void n.logMessage("Invalid fullname for Thai parsing");const a=e.trim().split(/\s+/);n.logData("Thai name parts",a),a.length>=1&&(t.titleTh=a[0],n.logData("Extracted titleTh",t.titleTh)),a.length>=2&&(t.firstNameTh=a[1],n.logData("Extracted firstNameTh",t.firstNameTh)),a.length>=3&&(t.lastNameTh=a.slice(2).join(" "),n.logData("Extracted lastNameTh",t.lastNameTh))}parseEnglishNameFields(e,t){if(!e||"string"!=typeof e)return void n.logMessage("Invalid firstName for English parsing");const a=e.trim().split(/\s+/);n.logData("English name parts",a),a.length>=1&&(t.titleEn=a[0],n.logData("Extracted titleEn",t.titleEn)),a.length>=2&&(t.firstNameEn=a.slice(1).join(" "),n.logData("Extracted firstNameEn",t.firstNameEn))}convertDateFields(e){["dateOfBirth","dateOfIssue","dateOfExpiry"].forEach((t=>{if(e[t]){const a=this.convertThaiDateFormat(e[t]);a?(n.logData(`Converted ${t}`,`${e[t]} -> ${a}`),e[t]=a):n.logError(`Failed to convert date field ${t}`,e[t])}}))}convertThaiDateFormat(e){if(!e||"string"!=typeof e)return null;try{const t={"ม.ค.":"01",มกราคม:"01","ม.ค":"01","ก.พ.":"02",กุมภาพันธ์:"02","ก.พ":"02","มี.ค.":"03",มีนาคม:"03","มี.ค":"03","เม.ย.":"04",เมษายน:"04","เม.ย":"04","พ.ค.":"05",พฤษภาคม:"05","พ.ค":"05","มิ.ย.":"06",มิถุนายน:"06","มิ.ย":"06","ก.ค.":"07",กรกฎาคม:"07","ก.ค":"07","ส.ค.":"08",สิงหาคม:"08","ส.ค":"08","ก.ย.":"09",กันยายน:"09","ก.ย":"09","ต.ค.":"10",ตุลาคม:"10","ต.ค":"10","พ.ย.":"11",พฤศจิกายน:"11","พ.ย":"11","ธ.ค.":"12",ธันวาคม:"12","ธ.ค":"12"},a=e.trim();if(/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(a))return n.logData("Date already in standard format",a),a;const s=a.split(/\s+/);if(s.length>=3){const a=s[0].padStart(2,"0"),o=s[1],r=s[2],i=t[o];if(i&&/^\d{4}$/.test(r)){const t=`${a}/${i}/${r}`;return n.logData("Thai date conversion successful",`${e} -> ${t}`),t}}const o=a.split("/");if(3===o.length){const a=o[0].padStart(2,"0"),s=o[1],r=o[2],i=t[s];if(i&&/^\d{4}$/.test(r)){const t=`${a}/${i}/${r}`;return n.logData("Thai date conversion (slash format) successful",`${e} -> ${t}`),t}}return n.logError("Unable to parse Thai date format",e),null}catch(e){return n.logError("Error converting Thai date format",e),null}}addFieldEditabilityMetadata(e){const t=["titleTh","firstNameTh","lastNameTh","titleEn","firstNameEn","lastNameEn","dateOfBirth","dateOfIssue","dateOfExpiry"],a=["nationalId","laserId"];e._fieldMetadata={editableFields:t,readOnlyFields:a,dateFields:["dateOfBirth","dateOfIssue","dateOfExpiry"],requiredFields:["nationalId","titleTh","firstNameTh","lastNameTh","dateOfBirth","dateOfExpiry"],optionalFields:["middleNameTh","middleNameEn","titleEn","firstNameEn","lastNameEn","dateOfIssue","laserId"]},Object.keys(e).forEach((s=>{"_fieldMetadata"!==s&&null!==e[s]&&(e[`_${s}Properties`]={isEditable:t.includes(s),isReadOnly:a.includes(s),isDate:["dateOfBirth","dateOfIssue","dateOfExpiry"].includes(s),isRequired:["nationalId","titleTh","firstNameTh","lastNameTh","dateOfBirth","dateOfExpiry"].includes(s),originalValue:e[s],hasBeenModified:!1})})),n.logData("Added field editability metadata",{editableCount:t.length,readOnlyCount:a.length,dateFieldCount:3})}}},719:e=>{e.exports=class{static logMessage(e,t="info"){const a=`[${(new Date).toISOString()}] [${t.toUpperCase()}]`;switch(t){case"error":console.error(`${a} ${e}`);break;case"warn":console.warn(`${a} ${e}`);break;case"success":console.log(`%c${a} ${e}`,"color: green; font-weight: bold;");break;default:console.log(`${a} ${e}`)}}static logFaceTecStatus(e){this.logMessage(`FaceTec SDK: ${e}`,"info")}static logApiCall(e,t,a){this.logMessage(`API ${t} ${e}: ${a}`,"info")}static logSuccess(e){this.logMessage(e,"success")}static logError(e,t=null){let a=e;t&&(a+=` - ${t.message}`),this.logMessage(a,"error"),t&&t.stack&&console.error("Stack trace:",t.stack)}static logWarning(e){this.logMessage(e,"warn")}static logIDScanProgress(e,t=null){let a=`ID Scan: ${e}`;null!==t&&(a+=` (${Math.round(100*t)}%)`),this.logMessage(a,"info")}static logLivenessProgress(e,t=null){let a=`Liveness Check: ${e}`;null!==t&&(a+=` (${Math.round(100*t)}%)`),this.logMessage(a,"info")}static logSession(e,t){this.logMessage(`Session ${e}: ${t}`,"info")}static clearConsole(){"function"==typeof console.clear&&console.clear()}static logData(e,t){console.group(`📊 ${e}`),console.log(t),console.groupEnd()}static logPerformance(e,t,a){const s=a-t;this.logMessage(`Performance: ${e} took ${s.toFixed(2)}ms`,"info")}}},777:(e,t,a)=>{const s=a(719);e.exports=class{constructor(){this.baseUrl="/api"}async postLivenessCheck(e,t={},a=null){return new Promise(((o,n)=>{const r=performance.now();try{const i=`${this.baseUrl}/enrollment-3d`;console.log("=== LIVENESS CHECK DATA SOURCE - STARTING REQUEST ==="),console.log("URL:",i),console.log("Input livenessData:",JSON.stringify(e,null,2)),console.log("Input headers:",JSON.stringify(t,null,2)),console.log("Liveness data keys:",Object.keys(e||{})),console.log("Liveness data structure check:",{hasFunction:!!e?.function,hasFaceScan:!!e?.faceScan,hasAuditTrailImage:!!e?.auditTrailImage,hasLowQualityAuditTrailImage:!!e?.lowQualityAuditTrailImage,hasSessionId:!!e?.sessionId}),s.logApiCall(i,"POST","Starting request");const c=new XMLHttpRequest;a&&"function"==typeof a&&(c.upload.onprogress=function(e){if(e.lengthComputable){const t=e.loaded/e.total;s.logLivenessProgress("Uploading",t),a(t)}}),c.onreadystatechange=function(){if(c.readyState===XMLHttpRequest.DONE){const e=performance.now();try{if(console.log("=== LIVENESS CHECK DATA SOURCE - RESPONSE RECEIVED ==="),console.log("Response status:",c.status),console.log("Response text:",c.responseText),c.status>=200&&c.status<300){const t=JSON.parse(c.responseText);console.log("✅ SUCCESS - Parsed response data:",JSON.stringify(t,null,2)),s.logPerformance("LivenessCheckDataSource.postLivenessCheck",r,e),s.logApiCall(i,"POST",`Success (${c.status})`),s.logData("API Response",{status:c.status,wasProcessed:t.wasProcessed,error:t.error,hasScanResultBlob:!!t.scanResultBlob}),o(t)}else console.log("❌ ERROR - HTTP error status:",c.status),console.log("Error response text:",c.responseText),s.logPerformance("LivenessCheckDataSource.postLivenessCheck (failed)",r,e),s.logError(`API call failed with status ${c.status}`),n(new Error(`HTTP error! status: ${c.status}`))}catch(e){s.logError("Error parsing response",e),n(e)}}},c.onerror=function(){s.logError("Network error occurred"),n(new Error("Network error occurred"))},c.open("POST",i,!0),c.setRequestHeader("Content-Type","application/json"),console.log("Setting Content-Type header: application/json");for(const[e,a]of Object.entries(t))null!=a?(c.setRequestHeader(e,a),console.log(`Setting header: ${e} = ${a}`)):console.log(`Skipping header (undefined/null): ${e} = ${a}`);const l=JSON.stringify(e);console.log("=== LIVENESS CHECK DATA SOURCE - SENDING REQUEST ==="),console.log("Request body (stringified):",l),console.log("Request body size (bytes):",l.length),console.log("Request body keys from original data:",Object.keys(e)),s.logData("Request Body Keys",Object.keys(e)),console.log("📤 SENDING XMLHttpRequest..."),c.send(l)}catch(e){s.logError("Error in postLivenessCheck",e),n(e)}}))}}},863:e=>{e.exports=class{execute(e){return`Hello 12, ${e}!`}}},955:(e,t,a)=>{const s=a(0),o=a(719);e.exports=function(e,t,a,n){var r=this;this.latestNetworkRequest=new XMLHttpRequest,this.deviceKey=a||null,this.additionalHeaders=n||{},this.postIDScanOnlyUseCase=new s,this.processIDScanResultWhileFaceTecSDKWaits=function(e,t){if(r.latestIDScanResult=e,e.status!==FaceTecSDK.FaceTecIDScanStatus.Success)return r.latestNetworkRequest.abort(),r.latestNetworkRequest=new XMLHttpRequest,void t.cancel();r.executeIDScanUseCase(e,t)},this.executeIDScanUseCase=async function(e,t){try{const a=function(e){t.uploadProgress(e)},s=await r.postIDScanOnlyUseCase.execute({idScanResult:e,deviceKey:r.deviceKey,additionalHeaders:r.additionalHeaders,onProgress:a});s.success?(FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides("Front Scan Complete","Front of ID<br/>Scanned","ID Scan Complete","Passport Scan Complete","Photo ID Scan<br/>Complete","ID Photo Capture<br/>Complete","Face Didn't Match<br/>Highly Enough","ID Document<br/>Not Fully Visible","ID Text Not Legible","ID Type Mismatch<br/>Please Try Again"),t.proceedToNextStep(s.scanResultBlob)):r.cancelDueToNetworkError(s.errorMessage||"Unexpected API response, cancelling out.",t)}catch(e){console.error("PhotoIDScanProcessor - executeIDScanUseCase error:",e),r.cancelDueToNetworkError(e.message||"Exception while handling API response, cancelling out.",t)}},this.onFaceTecSDKCompletelyDone=function(){null!==r.latestIDScanResult&&(r.success=r.latestIDScanResult.isCompletelyDone),r.success&&o.logMessage("Id Scan Complete"),r.sampleAppControllerReference.onComplete(null,r.latestIDScanResult,200)},this.cancelDueToNetworkError=function(e,t){!1===r.cancelledDueToNetworkError&&(console.error(e),r.cancelledDueToNetworkError=!0,t.cancel())},this.isSuccess=function(){return r.success},this.success=!1,this.sampleAppControllerReference=t,this.latestIDScanResult=null,this.cancelledDueToNetworkError=!1,FaceTecSDK.FaceTecCustomization.setIDScanUploadMessageOverrides("Uploading<br/>Encrypted<br/>ID Scan","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>ID Scan","Uploading<br/>Encrypted<br/>Back of ID","Still Uploading...<br/>Slow Connection","Upload Complete","Processing<br/>Back of ID","Uploading<br/>Your Confirmed Info","Still Uploading...<br/>Slow Connection","Info Saved","Processing"),new FaceTecSDK.FaceTecSession(this,e)}},985:e=>{e.exports=class{constructor(e){this.authRepository=e}async execute(e={}){return await this.authRepository.getFaceTecSessionTokenWithEkycToken(e)}}},995:e=>{e.exports=class{constructor(e){this.authRepository=e}async execute(e={}){return await this.authRepository.getSessionToken(e)}}}},t={},function a(s){var o=t[s];if(void 0!==o)return o.exports;var n=t[s]={exports:{}};return e[s](n,n.exports,a),n.exports}(347);var e,t}));