!function(e,s){"object"==typeof exports&&"object"==typeof module?module.exports=s():"function"==typeof define&&define.amd?define("api",[],s):"object"==typeof exports?exports.api=s():e.api=s()}(this,(()=>{return e={22:e=>{function s(e,s){s.status(200).json({status:"UP"})}e.exports=s,e.exports.default=s},137:e=>{async function s(e,s){if("POST"!==e.method)return s.status(405).json({error:"Method not allowed"});try{const o=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/match-3d-2d-idscan/front`,t={"Content-Type":"application/json",Accept:"application/json",Authorization:e.headers.authorization||(process.env.JWT_TOKEN?`Bearer ${process.env.JWT_TOKEN}`:void 0),"X-Device-Key":e.headers["x-device-key"],"X-User-Agent":e.headers["x-user-agent"],"X-Session-Id":e.headers["x-session-id"],"X-Tid":e.headers["x-tid"],"X-Ekyc-Token":e.headers["x-ekyc-token"],"X-Ekyc-Sdk-Version":e.headers["x-ekyc-sdk-version"]||"1.0.0",correlationid:e.headers.correlationid};Object.keys(t).forEach((e=>{void 0===t[e]&&delete t[e]})),console.log("Front ID scan request to:",o),console.log("Request headers:",Object.keys(t));const n=await fetch(o,{method:"POST",headers:t,body:JSON.stringify(e.body)}),r=await n.json();if(console.log("Front ID scan response status:",n.status),console.log("Front ID scan response code:",r.code),n.ok&&"CUS-KYC-1000"===r.code){const e={wasProcessed:!0,error:!1,scanResultBlob:r.data?.scanResultBlob||"",originalResponse:r};return console.log("Returning front scan success response with scanResultBlob length:",e.scanResultBlob.length),s.status(200).json(e)}{const e={wasProcessed:!1,error:!0,errorMessage:r.description||r.message||"Unknown error occurred during front ID scan",originalResponse:r};return console.log("Returning front scan error response:",e.errorMessage),s.status(n.status).json(e)}}catch(e){return console.error("Error processing front ID scan:",e),s.status(500).json({wasProcessed:!1,error:!0,errorMessage:"Failed to process front ID scan"})}}e.exports=s,e.exports.default=s},154:(e,s,o)=>{const t=o(198),n=o(264),r=o(235),c=o(137),a=o(991),i=o(622),d=o(22);e.exports={sessionTokenHandler:t,facetecSessionTokenHandler:n,idscanOnlyHandler:r,frontIdScanHandler:c,backIdScanHandler:a,livenessCheckHandler:i,healthHandler:d}},198:(e,s,o)=>{const t=o(599);async function n(e,s){if("GET"!==e.method)return s.status(405).json({error:"Method not allowed"});try{const o=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/authen/sessiontoken`,n={...e.headers};if(delete n.host,delete n.connection,n["Content-Type"]="application/json",n.Accept="application/json",!n.Authorization&&process.env.JWT_TOKEN&&(n.Authorization=`Bearer ${process.env.JWT_TOKEN}`),!n["X-Ekyc-Device-Info"]){const e=t.getUniqueId();n["X-Ekyc-Device-Info"]=`browser|${e}`}const r=await fetch(o,{method:"GET",headers:n}),c=await r.json();return s.status(r.status).json(c)}catch(e){return console.error("Error getting session token:",e),s.status(500).json({error:"Failed to get session token"})}}e.exports=n,e.exports.default=n},235:e=>{async function s(e,s){if("POST"!==e.method)return s.status(405).json({error:"Method not allowed"});try{const o=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/idscan-only`,t={"Content-Type":"application/json",Accept:"application/json",Authorization:e.headers.authorization||(process.env.JWT_TOKEN?`Bearer ${process.env.JWT_TOKEN}`:void 0),"X-Device-Key":e.headers["x-device-key"],"X-User-Agent":e.headers["x-user-agent"],"X-Session-Id":e.headers["x-session-id"],"X-Tid":e.headers["x-tid"],"X-Ekyc-Token":e.headers["x-ekyc-token"],correlationid:e.headers.correlationid};Object.keys(t).forEach((e=>{void 0===t[e]&&delete t[e]}));const n=await fetch(o,{method:"POST",headers:t,body:JSON.stringify(e.body)}),r=await n.json();if(n.ok&&"CUS-KYC-1000"===r.code){const e={wasProcessed:!0,error:!1,scanResultBlob:r.data?.scanResultBlob||"",originalResponse:r};return console.log("Returning success response with scanResultBlob length:",e.scanResultBlob.length),s.status(200).json(e)}{const e={wasProcessed:!1,error:!0,errorMessage:r.description||r.message||"Unknown error",originalResponse:r};return console.log("Returning error response:",e.errorMessage),s.status(n.status).json(e)}}catch(e){return console.error("Error processing ID scan:",e),s.status(500).json({wasProcessed:!1,error:!0,errorMessage:"Failed to process ID scan"})}}e.exports=s,e.exports.default=s},264:(e,s,o)=>{const t=o(599);async function n(e,s){if("GET"!==e.method)return s.status(405).json({error:"Method not allowed"});try{const o=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/authen/sessiontoken/facetec`,n={...e.headers};if(delete n.host,delete n.connection,n["Content-Type"]="application/json",n.Accept="application/json",!n.Authorization&&process.env.JWT_TOKEN&&(n.Authorization=`Bearer ${process.env.JWT_TOKEN}`),!n["X-Ekyc-Device-Info"]){const e=t.getUniqueId();n["X-Ekyc-Device-Info"]=`browser|${e}`}const r=await fetch(o,{method:"GET",headers:n}),c=await r.json();return s.status(r.status).json(c)}catch(e){return console.error("Error getting FaceTec session token:",e),s.status(500).json({error:"Failed to get FaceTec session token"})}}e.exports=n,e.exports.default=n},599:e=>{e.exports=class{static generateUuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const s=16*Math.random()|0;return("x"===e?s:3&s|8).toString(16)}))}static getDeviceId(){if("undefined"!=typeof window&&window.localStorage){let e=localStorage.getItem("ekyc_device_id");return e||(e=this.generateUuid(),localStorage.setItem("ekyc_device_id",e)),e}return this.generateUuid()}static getUniqueId(){return this.generateUuid()}}},622:e=>{async function s(e,s){if("POST"!==e.method)return s.status(405).json({error:"Method not allowed"});try{const o=(e,s=20)=>{if(!e||"object"!=typeof e)return e;const t={};for(const[n,r]of Object.entries(e))"string"==typeof r&&r.length>s?t[n]=r.substring(0,s)+"...":t[n]="object"==typeof r&&null!==r?o(r,s):r;return t};console.log("=== ENROLLMENT-3D API HANDLER - INCOMING REQUEST ==="),console.log("Request method:",e.method),console.log("Request headers received (truncated):",JSON.stringify(o(e.headers),null,2)),console.log("Request body received (truncated):",JSON.stringify(o(e.body),null,2)),console.log("Request body keys:",Object.keys(e.body||{}));const t=["function","faceScan","auditTrailImage","lowQualityAuditTrailImage"].filter((s=>!e.body||!e.body[s]));t.length>0?console.log("⚠️  MISSING REQUIRED BODY PARAMETERS:",t):console.log("✅ All required body parameters present");const n=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/enrollment-3d`,r={"Content-Type":"application/json",Accept:"application/json",Authorization:e.headers.authorization||(process.env.JWT_TOKEN?`Bearer ${process.env.JWT_TOKEN}`:void 0),"X-Device-Key":e.headers["x-device-key"],"X-User-Agent":e.headers["x-user-agent"],"X-Session-Id":e.headers["x-session-id"],"X-Tid":e.headers["x-tid"],"X-Ekyc-Token":e.headers["x-ekyc-token"],"X-Ekyc-Sdk-Version":"1.0.0",correlationid:e.headers.correlationid};Object.keys(r).forEach((e=>{void 0===r[e]&&delete r[e]})),console.log("=== ENROLLMENT-3D API HANDLER - OUTGOING REQUEST ==="),console.log("Target URL:",n),console.log("Outgoing headers (truncated):",JSON.stringify(o(r),null,2)),console.log("Outgoing body (truncated):",JSON.stringify(o(e.body),null,2)),console.log("Outgoing body size (bytes):",JSON.stringify(e.body).length);const c=await fetch(n,{method:"POST",headers:r,body:JSON.stringify(e.body)}),a=await c.json();if(console.log("=== ENROLLMENT-3D API HANDLER - BACKEND RESPONSE ==="),console.log("Response status:",c.status),console.log("Response ok:",c.ok),console.log("Response data:",JSON.stringify(a,null,2)),c.ok&&"CUS-KYC-1000"===a.code){const e={wasProcessed:!0,error:!1,scanResultBlob:a.data?.scanResultBlob||"",originalResponse:a};return console.log("✅ SUCCESS - Returning success response with scanResultBlob length:",e.scanResultBlob.length),console.log("=== ENROLLMENT-3D API HANDLER - FINAL RESPONSE ==="),console.log("Final response:",JSON.stringify(e,null,2)),s.status(200).json(e)}{const e={wasProcessed:!1,error:!0,errorMessage:a.description||a.message||"Unknown error",originalResponse:a};return console.log("❌ ERROR - Returning error response:",e.errorMessage),console.log("=== ENROLLMENT-3D API HANDLER - FINAL RESPONSE ==="),console.log("Final response:",JSON.stringify(e,null,2)),s.status(c.status).json(e)}}catch(e){return console.error("Error processing liveness check:",e),s.status(500).json({wasProcessed:!1,error:!0,errorMessage:"Failed to process liveness check"})}}e.exports=s,e.exports.default=s},991:e=>{async function s(e,s){if("POST"!==e.method)return s.status(405).json({error:"Method not allowed"});try{const o=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/match-3d-2d-idscan/back`,t={"Content-Type":"application/json",Accept:"application/json",Authorization:e.headers.authorization||(process.env.JWT_TOKEN?`Bearer ${process.env.JWT_TOKEN}`:void 0),"X-Device-Key":e.headers["x-device-key"],"X-User-Agent":e.headers["x-user-agent"],"X-Session-Id":e.headers["x-session-id"],"X-Tid":e.headers["x-tid"],"X-Ekyc-Token":e.headers["x-ekyc-token"],"X-Ekyc-Sdk-Version":e.headers["x-ekyc-sdk-version"]||"1.0.0",correlationid:e.headers.correlationid};Object.keys(t).forEach((e=>{void 0===t[e]&&delete t[e]})),console.log("Back ID scan request to:",o),console.log("Request headers:",Object.keys(t));const n=await fetch(o,{method:"POST",headers:t,body:JSON.stringify(e.body)}),r=await n.json();if(console.log("Back ID scan response status:",n.status),console.log("Back ID scan response code:",r.code),n.ok&&"CUS-KYC-1000"===r.code){const e={wasProcessed:!0,error:!1,scanResultBlob:r.data?.scanResultBlob||"",originalResponse:r};return console.log("Returning back scan success response with scanResultBlob length:",e.scanResultBlob.length),s.status(200).json(e)}{const e={wasProcessed:!1,error:!0,errorMessage:r.description||r.message||"Unknown error occurred during back ID scan",originalResponse:r};return console.log("Returning back scan error response:",e.errorMessage),s.status(n.status).json(e)}}catch(e){return console.error("Error processing back ID scan:",e),s.status(500).json({wasProcessed:!1,error:!0,errorMessage:"Failed to process back ID scan"})}}e.exports=s,e.exports.default=s}},s={},function o(t){var n=s[t];if(void 0!==n)return n.exports;var r=s[t]={exports:{}};return e[t](r,r.exports,o),r.exports}(154);var e,s}));