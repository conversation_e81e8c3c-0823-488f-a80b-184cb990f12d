//
// Welcome to the annotated FaceTec Device SDK core code for performing a secure Photo ID Scan.
//
//
// This is an example self-contained class to perform Photo ID Scans with the FaceTec SDK.
// You may choose to further componentize parts of this in your own Apps based on your specific requirements.
//

const PostIDScanOnlyUseCase = require('../domain/usecases/PostIDScanOnlyUseCase');
const DeveloperStatusMessages = require('../infrastructure/utils/DeveloperStatusMessages');

var PhotoIDScanProcessor = /** @class */ (function () {
    function PhotoIDScanProcessor(sessionToken, sampleAppControllerReference, deviceKey, additionalHeaders, ocrResultsCallback) {
        var _this = this;
        this.latestNetworkRequest = new XMLHttpRequest();
        this.deviceKey = deviceKey || null; // Store the device key
        this.additionalHeaders = additionalHeaders || {}; // get additional headers
        this.ocrResultsCallback = ocrResultsCallback || null; // Store the OCR results callback
        this.postIDScanOnlyUseCase = new PostIDScanOnlyUseCase(); // Initialize use case
        
        //
        // Part 2:  Handling the Result of a Photo ID Scan
        //
        this.processIDScanResultWhileFaceTecSDKWaits = function (idScanResult, idScanResultCallback) {
            _this.latestIDScanResult = idScanResult;
            //
            // Part 3:  Handles early exit scenarios where there is no Photo ID Scan result to handle -- i.e. User Cancellation, Timeouts, etc.
            //
            if (idScanResult.status !== FaceTecSDK.FaceTecIDScanStatus.Success) {
                _this.latestNetworkRequest.abort();
                _this.latestNetworkRequest = new XMLHttpRequest();
                idScanResultCallback.cancel();
                return;
            }
            // IMPORTANT:  FaceTecSDK.FaceTecIDScanStatus.Success DOES NOT mean the Photo ID Scan was Successful.
            // It simply means the User completed the Session.  You still need to perform the Photo ID Scan result checks on your Servers.
            
            //
            // Part 4: Use Clean Architecture - Call UseCase instead of direct API
            //
            _this.executeIDScanUseCase(idScanResult, idScanResultCallback);
        };

        //
        // New method: Execute ID Scan using Clean Architecture UseCase
        //
        this.executeIDScanUseCase = async function(idScanResult, idScanResultCallback) {
            try {
                // Create progress callback for upload tracking
                const onProgress = function(progress) {
                    idScanResultCallback.uploadProgress(progress);
                };

                // Execute the use case
                const result = await _this.postIDScanOnlyUseCase.execute({
                    idScanResult: idScanResult,
                    deviceKey: _this.deviceKey,
                    additionalHeaders: _this.additionalHeaders,
                    onProgress: onProgress
                });

                // Handle the result
                if (result.success) {
                    // Configure success messages
                    FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides(
                        "Front Scan Complete", // Successful scan of ID front-side (ID Types with no back-side).
                        "Front of ID<br/>Scanned", // Successful scan of ID front-side (ID Types that do have a back-side).
                        "ID Scan Complete", // Successful scan of the ID back-side.
                        "Passport Scan Complete", // Successful scan of a Passport
                        "Photo ID Scan<br/>Complete", // Successful upload of final Photo ID Scan result containing User-Confirmed ID Text.
                        "ID Photo Capture<br/>Complete", // Successful upload of final Photo ID Scan result but scan is flagged as requiring additional review.
                        "Face Didn't Match<br/>Highly Enough", // Case where a Retry is needed because the Face on the Photo ID did not Match the User's Face highly enough.
                        "ID Document<br/>Not Fully Visible", // Case where a Retry is needed because a Full ID was not detected with high enough confidence.
                        "ID Text Not Legible", // Case where a Retry is needed because the OCR did not produce good enough results and the User should Retry with a better capture.
                        "ID Type Mismatch<br/>Please Try Again" // Case where there is likely no OCR Template installed for the document the User is attempting to scan.
                    );
                    
                    // Proceed to next step with scanResultBlob
                    idScanResultCallback.proceedToNextStep(result.scanResultBlob);
                } else {
                    // Store callback data for error case
                    if (_this.ocrResultsCallback) {
                        _this.callbackResult = {
                            success: false,
                            description: result.errorMessage || "Unexpected API response",
                            userOcrValue: null,
                            userConfirmedValue: null,
                            dopaResult: null,
                            timestamp: new Date().toISOString()
                        };
                    }

                    // Handle error case
                    _this.cancelDueToNetworkError(result.errorMessage || "Unexpected API response, cancelling out.", idScanResultCallback);
                }
            } catch (error) {
                console.error('PhotoIDScanProcessor - executeIDScanUseCase error:', error);

                // Store callback data for exception case
                if (_this.ocrResultsCallback) {
                    _this.callbackResult = {
                        success: false,
                        description: "Exception while handling API response: " + error.message,
                        userOcrValue: null,
                        userConfirmedValue: null,
                        dopaResult: null,
                        timestamp: new Date().toISOString()
                    };
                }

                _this.cancelDueToNetworkError(error.message || "Exception while handling API response, cancelling out.", idScanResultCallback);
            }
        };

        //
        // Part 9:  This function gets called after the FaceTec SDK is completely done.  There are no parameters because you have already been passed all data in the processSessionWhileFaceTecSDKWaits function and have already handled all of your own results.
        //
        this.onFaceTecSDKCompletelyDone = function () {
            //
            // DEVELOPER NOTE:  onFaceTecSDKCompletelyDone() is called after the Session has completed or you signal the FaceTec SDK with cancel().
            // Calling a custom function on the Sample App Controller is done for demonstration purposes to show you that here is where you get control back from the FaceTec SDK.
            //
            // If the Photo ID Scan was processed get the success result from isCompletelyDone
            if (_this.latestIDScanResult !== null) {
                _this.success = _this.latestIDScanResult.isCompletelyDone;
            }
            // Log success message
            if (_this.success) {
                DeveloperStatusMessages.logMessage("Id Scan Complete");
            }
            _this.sampleAppControllerReference.onComplete(null, _this.latestIDScanResult, 200); // Use 200 as default status
        };

        // Helper function to ensure the session is only cancelled once
        this.cancelDueToNetworkError = function (networkErrorMessage, faceTecIdScanResultCallback) {
            if (_this.cancelledDueToNetworkError === false) {
                console.error(networkErrorMessage);
                _this.cancelledDueToNetworkError = true;
                faceTecIdScanResultCallback.cancel();
            }
        };

        //
        // Method to get stored callback result
        //
        this.getCallbackResult = function() {
            return _this.callbackResult || null;
        };

        //
        // Method to execute stored callback if available
        //
        this.executeStoredCallback = function() {
            if (_this.ocrResultsCallback && _this.callbackResult) {
                DeveloperStatusMessages.logMessage('Executing stored OCR results callback');
                DeveloperStatusMessages.logData('Callback Result', _this.callbackResult);

                _this.ocrResultsCallback(
                    _this.callbackResult.success,
                    _this.callbackResult.description,
                    _this.callbackResult.userOcrValue,
                    _this.callbackResult.userConfirmedValue,
                    _this.callbackResult.dopaResult
                );

                return true; // Callback was executed
            }
            return false; // No callback to execute
        };

        //
        // DEVELOPER NOTE:  This public convenience method is for demonstration purposes only so the Sample App can get information about what is happening in the processor.
        // In your code, you may not even want or need to do this.
        //
        this.isSuccess = function () {
            return _this.success;
        };

        //
        // DEVELOPER NOTE:  These properties are for demonstration purposes only so the Sample App can get information about what is happening in the processor.
        // In the code in your own App, you can pass around signals, flags, intermediates, and results however you would like.
        //
        this.success = false;
        this.sampleAppControllerReference = sampleAppControllerReference;
        this.latestIDScanResult = null;
        this.cancelledDueToNetworkError = false;

        // In v9.2.2+, configure the messages that will be displayed to the User in each of the possible cases.
        // Based on the internal processing and decision logic about how the flow gets advanced, the FaceTec SDK will use the appropriate, configured message.
        FaceTecSDK.FaceTecCustomization.setIDScanUploadMessageOverrides(
            "Uploading<br/>Encrypted<br/>ID Scan", // Upload of ID front-side has started.
            "Still Uploading...<br/>Slow Connection", // Upload of ID front-side is still uploading to Server after an extended period of time.
            "Upload Complete", // Upload of ID front-side to the Server is complete.
            "Processing<br/>ID Scan", // Upload of ID front-side is complete and we are waiting for the Server to finish processing and respond.
            "Uploading<br/>Encrypted<br/>Back of ID", // Upload of ID back-side has started.
            "Still Uploading...<br/>Slow Connection", // Upload of ID back-side is still uploading to Server after an extended period of time.
            "Upload Complete", // Upload of ID back-side to Server is complete.
            "Processing<br/>Back of ID", // Upload of ID back-side is complete and we are waiting for the Server to finish processing and respond.
            "Uploading<br/>Your Confirmed Info", // Upload of User Confirmed Info has started.
            "Still Uploading...<br/>Slow Connection", // Upload of User Confirmed Info is still uploading to Server after an extended period of time.
            "Info Saved", // Upload of User Confirmed Info to the Server is complete.
            "Processing" // Upload of User Confirmed Info is complete and we are waiting for the Server to finish processing and respond.
        );

        //
        // Part 1:  Starting the FaceTec Photo ID Scan Session
        //
        // Required parameters:
        // - FaceTecIDScanProcessor:  A class that implements FaceTecIDScanProcessor, which handles the Photo ID Scan when the User completes a Session.  In this example, "this" implements the class.
        // - sessionToken:  A valid Session Token you just created by calling your API to get a Session Token from the Server SDK.
        //
        new FaceTecSDK.FaceTecSession(this, sessionToken);
    }
    return PhotoIDScanProcessor;
}());

// Export the PhotoIDScanProcessor class for use in Node.js/webpack environments
module.exports = PhotoIDScanProcessor;
