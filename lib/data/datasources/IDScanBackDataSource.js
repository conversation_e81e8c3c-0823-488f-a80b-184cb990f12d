const DeveloperStatusMessages = require('../../infrastructure/utils/DeveloperStatusMessages');

/**
 * Data source for back ID scan operations
 * Handles HTTP requests to the back ID scan API endpoint
 */
class IDScanBackDataSource {
    constructor() {
        this.baseUrl = '/lib/infrastructure/api';
    }

    /**
     * Post back ID scan data to the API
     * @param {Object} scanData - The scan data including back image and metadata
     * @param {Object} headers - Additional headers for the request
     * @param {Function} onProgress - Progress callback function
     * @returns {Promise<Object>} - API response
     */
    async postIDScanBack(scanData, headers = {}, onProgress = null) {
        const startTime = performance.now();
        
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            const url = `${this.baseUrl}/match-3d-2d-idscan-back`;

            DeveloperStatusMessages.logMessage(`Starting back ID scan request to: ${url}`);

            // Set up progress tracking
            if (onProgress && typeof onProgress === 'function') {
                xhr.upload.addEventListener('progress', (event) => {
                    if (event.lengthComputable) {
                        const progress = (event.loaded / event.total) * 100;
                        onProgress(progress);
                        DeveloperStatusMessages.logData('Back scan upload progress', `${progress.toFixed(1)}%`);
                    }
                });
            }

            xhr.addEventListener('loadend', () => {
                const endTime = performance.now();
                
                if (xhr.readyState === XMLHttpRequest.DONE) {
                    try {
                        if (xhr.status >= 200 && xhr.status < 300) {
                            const responseData = JSON.parse(xhr.responseText);
                            DeveloperStatusMessages.logPerformance('IDScanBackDataSource.postIDScanBack', startTime, endTime);
                            DeveloperStatusMessages.logApiCall(url, 'POST', `Success (${xhr.status})`);
                            DeveloperStatusMessages.logData('Back API Response', {
                                status: xhr.status,
                                wasProcessed: responseData.wasProcessed,
                                error: responseData.error,
                                hasScanResultBlob: !!responseData.scanResultBlob,
                                hasOcrData: !!(responseData.originalResponse?.data?.documentData),
                                scanType: 'back'
                            });
                            resolve(responseData);
                        } else {
                            DeveloperStatusMessages.logPerformance('IDScanBackDataSource.postIDScanBack (failed)', startTime, endTime);
                            DeveloperStatusMessages.logError(`Back API call failed with status ${xhr.status}`);
                            
                            // Try to parse error response
                            let errorData = null;
                            try {
                                errorData = JSON.parse(xhr.responseText);
                            } catch (parseError) {
                                DeveloperStatusMessages.logError('Failed to parse error response', parseError);
                            }
                            
                            reject(new Error(`HTTP error! status: ${xhr.status}, message: ${errorData?.errorMessage || 'Unknown error'}`));
                        }
                    } catch (error) {
                        DeveloperStatusMessages.logPerformance('IDScanBackDataSource.postIDScanBack (parse error)', startTime, endTime);
                        DeveloperStatusMessages.logError('Error parsing back scan response:', error);
                        reject(error);
                    }
                }
            });

            xhr.addEventListener('error', () => {
                const endTime = performance.now();
                DeveloperStatusMessages.logPerformance('IDScanBackDataSource.postIDScanBack (network error)', startTime, endTime);
                DeveloperStatusMessages.logError('Network error during back ID scan request');
                reject(new Error('Network error occurred during back ID scan'));
            });

            xhr.addEventListener('timeout', () => {
                const endTime = performance.now();
                DeveloperStatusMessages.logPerformance('IDScanBackDataSource.postIDScanBack (timeout)', startTime, endTime);
                DeveloperStatusMessages.logError('Back ID scan request timed out');
                reject(new Error('Back ID scan request timed out'));
            });

            // Configure request
            xhr.open('POST', url, true);
            xhr.timeout = 60000; // 60 second timeout
            
            // Set headers
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.setRequestHeader('Accept', 'application/json');
            
            // Add custom headers
            Object.keys(headers).forEach(key => {
                if (headers[key] !== undefined && headers[key] !== null) {
                    xhr.setRequestHeader(key, headers[key]);
                    DeveloperStatusMessages.logData(`Back scan header: ${key}`, headers[key]);
                }
            });

            // Prepare request body
            const requestBody = JSON.stringify(scanData);
            DeveloperStatusMessages.logData('Back scan request body size', `${requestBody.length} bytes`);
            DeveloperStatusMessages.logData('Back scan data keys', Object.keys(scanData));

            // Send request
            xhr.send(requestBody);
        });
    }

    /**
     * Validate back scan data before submission
     * @param {Object} scanData - The scan data to validate
     * @returns {boolean} - True if valid, throws error if invalid
     */
    validateBackScanData(scanData) {
        if (!scanData) {
            throw new Error('Back scan data is required');
        }

        if (!scanData.idScan) {
            throw new Error('ID scan data is required for back scan');
        }

        if (!scanData.idScanBackImage) {
            throw new Error('Back image is required for back ID scan');
        }

        DeveloperStatusMessages.logMessage('Back scan data validation passed');
        return true;
    }
}

module.exports = IDScanBackDataSource;
