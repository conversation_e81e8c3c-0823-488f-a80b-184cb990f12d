/**
 * API route to process back ID scan data to the eKYC backend API
 * This acts as a proxy to avoid CORS issues when calling from the browser
 *
 * @param {import('next').NextApiRequest} req
 * @param {import('next').NextApiResponse} res
 */
async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Set the base URL - this should be configured based on environment
    const baseUrl = process.env.API_BASE_URL || 'https://ekyc-ekyc-dev.np.scbtechx.io';
    const url = `${baseUrl}/v1/ekyc/match-3d-2d-idscan/back`;

    // ✅ Forward headers follow spec
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': req.headers['authorization'] || (process.env.JWT_TOKEN ? `Bearer ${process.env.JWT_TOKEN}` : undefined),
      'X-Device-Key': req.headers['x-device-key'],
      'X-User-Agent': req.headers['x-user-agent'],
      'X-Session-Id': req.headers['x-session-id'],
      'X-Tid': req.headers['x-tid'],
      'X-Ekyc-Token': req.headers['x-ekyc-token'],
      'X-Ekyc-Sdk-Version': req.headers['x-ekyc-sdk-version'] || '1.0.0',
      'correlationid': req.headers.correlationid
    };

    // Remove undefined headers
    Object.keys(headers).forEach(key => {
      if (headers[key] === undefined) {
        delete headers[key];
      }
    });

    console.log('Back ID scan request to:', url);
    console.log('Request headers:', Object.keys(headers));

    // Make the POST request with the body from the client
    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(req.body),
    });

    // Get the response data
    const data = await response.json();

    console.log('Back ID scan response status:', response.status);
    console.log('Back ID scan response code:', data.code);

    // Transform the response to match PhotoIDMatchProcessor expectations
    if (response.ok && data.code === 'CUS-KYC-1000') {
      // Success case - return format expected by PhotoIDMatchProcessor
      const successResponse = {
        wasProcessed: true,
        error: false,
        scanResultBlob: data.data?.scanResultBlob || "", // Use scanResultBlob from backend if available
        originalResponse: data // Keep original response for debugging
      };
      
      console.log('Returning back scan success response with scanResultBlob length:', successResponse.scanResultBlob.length);
      return res.status(200).json(successResponse);
    } else {
      // Error case - return format expected by PhotoIDMatchProcessor
      const errorResponse = {
        wasProcessed: false,
        error: true,
        errorMessage: data.description || data.message || 'Unknown error occurred during back ID scan',
        originalResponse: data // Keep original response for debugging
      };
      
      console.log('Returning back scan error response:', errorResponse.errorMessage);
      return res.status(response.status).json(errorResponse);
    }
  } catch (error) {
    console.error('Error processing back ID scan:', error);
    return res.status(500).json({
      wasProcessed: false,
      error: true,
      errorMessage: 'Failed to process back ID scan'
    });
  }
}

// Export for both CommonJS and ES modules
module.exports = handler;
module.exports.default = handler;
