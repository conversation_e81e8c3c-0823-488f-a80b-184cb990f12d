//
// Welcome to the annotated FaceTec Device SDK core code for performing secure Photo ID Scan.
//
//
// This is an example self-contained class to perform Photo ID Scans with the FaceTec SDK.
// You may choose to further componentize parts of this in your own Apps based on your specific requirements.
//

const PostIDScanOnlyUseCase = require('../domain/usecases/PostIDScanOnlyUseCase');
const PostIDScanFrontUseCase = require('../domain/usecases/PostIDScanFrontUseCase');
const PostIDScanBackUseCase = require('../domain/usecases/PostIDScanBackUseCase');
const PostLivenessCheckUseCase = require('../domain/usecases/PostLivenessCheckUseCase');
const DeveloperStatusMessages = require('../infrastructure/utils/DeveloperStatusMessages');
const OcrDataValidator = require('../infrastructure/utils/OcrDataValidator');

var PhotoIDMatchProcessor = /** @class */ (function () {
    function PhotoIDMatchProcessor(sessionToken, sampleAppControllerReference, deviceKey, additionalHeaders) {
        var _this = this;
        this.latestNetworkRequest = new XMLHttpRequest();
        this.deviceKey = deviceKey || null; // Store the device key
        this.additionalHeaders = additionalHeaders || {}; // get additional headers
        this.postIDScanOnlyUseCase = new PostIDScanOnlyUseCase(); // Initialize use case
        this.postIDScanFrontUseCase = new PostIDScanFrontUseCase(); // Initialize front scan use case
        this.postIDScanBackUseCase = new PostIDScanBackUseCase(); // Initialize back scan use case
        this.postLivenessCheckUseCase = new PostLivenessCheckUseCase(); // Initialize liveness check use case
        
        //
        // Part 2:  Handling the Result of a FaceScan - First part of the Photo ID Scan
        //
        this.processSessionResultWhileFaceTecSDKWaits = function (sessionResult, faceScanResultCallback) {
            _this.latestSessionResult = sessionResult;
            //
            // Part 3:  Handles early exit scenarios where there is no FaceScan to handle -- i.e. User Cancellation, Timeouts, etc.
            //
            if (sessionResult.status !== FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully) {
                _this.latestNetworkRequest.abort();
                _this.latestNetworkRequest = new XMLHttpRequest();
                faceScanResultCallback.cancel();
                return;
            }
            // IMPORTANT:  FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully DOES NOT mean the Liveness Check was Successful.
            // It simply means the User completed the Session and a 3D FaceScan was created.  You still need to perform the Liveness Check on your Servers.

            //
            // Part 4: Use Clean Architecture - Call UseCase instead of direct API
            //
            _this.executeLivenessCheckUseCase(sessionResult, faceScanResultCallback);
        };

        //
        // New method: Execute Liveness Check using Clean Architecture UseCase
        //
        this.executeLivenessCheckUseCase = async function(sessionResult, faceScanResultCallback) {
            try {
                // Create progress callback for upload tracking
                const onProgress = function(progress) {
                    faceScanResultCallback.uploadProgress(progress);
                };

                // Execute the use case
                const result = await _this.postLivenessCheckUseCase.execute({
                    sessionResult: sessionResult,
                    deviceKey: _this.deviceKey,
                    additionalHeaders: _this.additionalHeaders,
                    onProgress: onProgress
                });

                // Handle the result
                if (result.success) {
                    // Demonstrates dynamically setting the Success Screen Message.
                    FaceTecSDK.FaceTecCustomization.setOverrideResultScreenSuccessMessage("Face Scanned\n3D Liveness Proven");
                    // In v9.2.0+, simply pass in scanResultBlob to the proceedToNextStep function to advance the User flow.
                    // scanResultBlob is a proprietary, encrypted blob that controls the logic for what happens next for the User.
                    faceScanResultCallback.proceedToNextStep(result.scanResultBlob);
                } else {
                    // Handle error case
                    _this.cancelDueToNetworkError(result.errorMessage || "Liveness check failed", faceScanResultCallback);
                }
            } catch (error) {
                console.error("Liveness check use case error:", error);
                _this.cancelDueToNetworkError("Network error during liveness check", faceScanResultCallback);
            }
        };
        //
        // Part 10:  Handling the Result of a IDScan
        //
        this.processIDScanResultWhileFaceTecSDKWaits = function (idScanResult, idScanResultCallback) {
            _this.latestIDScanResult = idScanResult;
            //
            // Part 11:  Handles early exit scenarios where there is no IDScan to handle -- i.e. User Cancellation, Timeouts, etc.
            //
            if (idScanResult.status !== FaceTecSDK.FaceTecIDScanStatus.Success) {
                _this.latestNetworkRequest.abort();
                _this.latestNetworkRequest = new XMLHttpRequest();
                idScanResultCallback.cancel();
                return;
            }
            // IMPORTANT:  FaceTecSDK.FaceTecIDScanStatus.Success DOES NOT mean the IDScan was Successful.
            // It simply means the User completed the Session and a 3D IDScan was created.  You still need to perform the ID-Check on your Servers.

            //
            // Part 12: Use Clean Architecture - Call UseCase instead of direct API
            //
            _this.executeIDScanUseCase(idScanResult, idScanResultCallback);
        };

        //
        // New method: Execute ID Scan using Clean Architecture UseCase with endpoint routing
        //
        this.executeIDScanUseCase = async function(idScanResult, idScanResultCallback) {
            try {
                // Create progress callback for upload tracking
                const onProgress = function(progress) {
                    idScanResultCallback.uploadProgress(progress);
                };

                // Determine scan type and route to appropriate endpoint
                let result;
                let scanType = 'unknown';

                if (idScanResult.backImages && idScanResult.backImages[0]) {
                    // This is a back ID card scan - use back endpoint
                    scanType = 'back';
                    DeveloperStatusMessages.logMessage('Routing to back ID scan endpoint');
                    result = await _this.postIDScanBackUseCase.execute({
                        idScanResult: idScanResult,
                        deviceKey: _this.deviceKey,
                        additionalHeaders: _this.additionalHeaders,
                        onProgress: onProgress
                    });
                } else if (idScanResult.frontImages && idScanResult.frontImages[0]) {
                    // This is a front ID card scan - use front endpoint
                    scanType = 'front';
                    DeveloperStatusMessages.logMessage('Routing to front ID scan endpoint');
                    result = await _this.postIDScanFrontUseCase.execute({
                        idScanResult: idScanResult,
                        deviceKey: _this.deviceKey,
                        additionalHeaders: _this.additionalHeaders,
                        onProgress: onProgress
                    });
                }
                DeveloperStatusMessages.logData('ID scan completed', { scanType: scanType, success: result.success });

                // Handle the result
                if (result.success) {
                    // Check if this is a retry case (CUS-KYC-7102)
                    if (result.allowRetry) {
                        DeveloperStatusMessages.logMessage(`${scanType} scan: Retry allowed - ${result.retryReason}`);

                        // Configure retry-specific messages
                        _this.configureRetryMessages(scanType);

                        // For retry cases, proceed with scanResultBlob to allow retry
                        idScanResultCallback.proceedToNextStep(result.scanResultBlob);
                    } else {
                        // Normal success case
                        // Store callback data for success case (except for retry cases)
                        if (_this.ocrResultsCallback) {
                            // Extract OCR data for callback
                            const extractedOcrValue = _this.extractOcrValueForCallback(result);

                            _this.callbackResult = {
                                success: true,
                                description: `${scanType} ID scan completed successfully`,
                                userOcrValue: extractedOcrValue,
                                userConfirmedValue: null, // Will be set later if user confirms
                                dopaResult: null,
                                timestamp: new Date().toISOString()
                            };

                            DeveloperStatusMessages.logMessage('Stored callback result for success case');
                            DeveloperStatusMessages.logData('Success Callback Result', _this.callbackResult);
                        }

                        // Check if this is a back ID card scan and show success overlay
                        if (scanType === 'back' && result.userOcrValue) {
                            // This is a back ID card scan - show success overlay with OCR data
                            _this.showSuccessOverlay(result.userOcrValue);
                        }

                        // Configure success messages based on scan type
                        _this.configureSuccessMessages(scanType);

                        // In v9.2.0+, simply pass in scanResultBlob to the proceedToNextStep function to advance the User flow.
                        idScanResultCallback.proceedToNextStep(result.scanResultBlob);
                    }
                } else {
                    // Handle error case - store callback data for later execution (except for retry cases)
                    if (_this.ocrResultsCallback && !result.allowRetry) {
                        // Extract OCR data for callback even in error cases
                        const extractedOcrValue = _this.extractOcrValueForCallback(result);

                        _this.callbackResult = {
                            success: false,
                            description: result.errorMessage || `${scanType} ID scan failed`,
                            userOcrValue: extractedOcrValue,
                            userConfirmedValue: null,
                            dopaResult: null,
                            timestamp: new Date().toISOString()
                        };

                        DeveloperStatusMessages.logMessage('Stored callback result for error case');
                        DeveloperStatusMessages.logData('Error Callback Result', _this.callbackResult);
                    }

                    // Handle error case
                    _this.cancelDueToNetworkError(result.errorMessage || `${scanType} ID scan failed`, idScanResultCallback);
                }
            } catch (error) {
                console.error("ID scan use case error:", error);

                // Store callback data for network/exception errors
                if (_this.ocrResultsCallback) {
                    _this.callbackResult = {
                        success: false,
                        description: "Network error during ID scan: " + error.message,
                        userOcrValue: null,
                        userConfirmedValue: null,
                        dopaResult: null,
                        timestamp: new Date().toISOString()
                    };

                    DeveloperStatusMessages.logMessage('Stored callback result for exception case');
                    DeveloperStatusMessages.logData('Exception Callback Result', _this.callbackResult);
                }

                _this.cancelDueToNetworkError("Network error during ID scan", idScanResultCallback);
            }
        };

        //
        // Helper method to configure success messages based on scan type
        //
        this.configureSuccessMessages = function(scanType) {
            if (scanType === 'front') {
                FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides(
                    "Front Scan Complete", // Successful scan of ID front-side (ID Types with no back-side).
                    "Front of ID<br/>Scanned", // Successful scan of ID front-side (ID Types that do have a back-side).
                    "Front Scan Complete", // Successful scan of the ID front-side.
                    "Passport Scan Complete", // Successful scan of a Passport
                    "Photo ID Front<br/>Complete", // Successful upload of final Photo ID Front result.
                    "ID Photo Capture<br/>Complete", // Successful upload of final Photo ID Scan result but scan is flagged as requiring additional review.
                    "Face Didn't Match<br/>Highly Enough", // Case where a Retry is needed because the Face on the Photo ID did not Match the User's Face highly enough.
                    "ID Document<br/>Not Fully Visible", // Case where a Retry is needed because a Full ID was not detected with high enough confidence.
                    "ID Text Not Legible", // Case where a Retry is needed because the OCR did not produce good enough results and the User should Retry with a better capture.
                    "ID Type Mismatch<br/>Please Try Again" // Case where there is likely no OCR Template installed for the document the User is attempting to scan.
                );
            } else if (scanType === 'back') {
                FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides(
                    "Back Scan Complete", // Successful scan of ID back-side.
                    "Back of ID<br/>Scanned", // Successful scan of ID back-side.
                    "ID Scan Complete", // Successful scan of the ID back-side.
                    "Passport Scan Complete", // Successful scan of a Passport
                    "Photo ID Back<br/>Complete", // Successful upload of final Photo ID Back result containing OCR data.
                    "ID Photo Capture<br/>Complete", // Successful upload of final Photo ID Scan result but scan is flagged as requiring additional review.
                    "Face Didn't Match<br/>Highly Enough", // Case where a Retry is needed because the Face on the Photo ID did not Match the User's Face highly enough.
                    "ID Document<br/>Not Fully Visible", // Case where a Retry is needed because a Full ID was not detected with high enough confidence.
                    "ID Text Not Legible", // Case where a Retry is needed because the OCR did not produce good enough results and the User should Retry with a better capture.
                    "ID Type Mismatch<br/>Please Try Again" // Case where there is likely no OCR Template installed for the document the User is attempting to scan.
                );
            } else {
                // Default/legacy messages
                FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides(
                    "Front Scan Complete", // Successful scan of ID front-side (ID Types with no back-side).
                    "Front of ID<br/>Scanned", // Successful scan of ID front-side (ID Types that do have a back-side).
                    "ID Scan Complete", // Successful scan of the ID back-side.
                    "Passport Scan Complete", // Successful scan of a Passport
                    "Photo ID Scan<br/>Complete", // Successful upload of final Photo ID Scan result containing User-Confirmed ID Text.
                    "ID Photo Capture<br/>Complete", // Successful upload of final Photo ID Scan result but scan is flagged as requiring additional review.
                    "Face Didn't Match<br/>Highly Enough", // Case where a Retry is needed because the Face on the Photo ID did not Match the User's Face highly enough.
                    "ID Document<br/>Not Fully Visible", // Case where a Retry is needed because a Full ID was not detected with high enough confidence.
                    "ID Text Not Legible", // Case where a Retry is needed because the OCR did not produce good enough results and the User should Retry with a better capture.
                    "ID Type Mismatch<br/>Please Try Again" // Case where there is likely no OCR Template installed for the document the User is attempting to scan.
                );
            }
        };

        //
        // Helper method to configure retry messages for CUS-KYC-7102 cases
        //
        this.configureRetryMessages = function(scanType) {
            DeveloperStatusMessages.logMessage(`Configuring retry messages for ${scanType} scan (CUS-KYC-7102)`);

            if (scanType === 'front') {
                FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides(
                    "Front Scan Complete<br/>Please Try Again", // Successful scan of ID front-side (ID Types with no back-side).
                    "Front of ID Scanned<br/>Please Try Again", // Successful scan of ID front-side (ID Types that do have a back-side).
                    "Front Scan Complete<br/>Please Try Again", // Successful scan of the ID front-side.
                    "Passport Scan Complete<br/>Please Try Again", // Successful scan of a Passport
                    "Photo ID Front Complete<br/>Please Try Again", // Successful upload of final Photo ID Front result.
                    "ID Photo Capture Complete<br/>Please Try Again", // Successful upload of final Photo ID Scan result but scan is flagged as requiring additional review.
                    "Face Didn't Match<br/>Highly Enough", // Case where a Retry is needed because the Face on the Photo ID did not Match the User's Face highly enough.
                    "ID Document<br/>Not Fully Visible", // Case where a Retry is needed because a Full ID was not detected with high enough confidence.
                    "ID Text Not Legible", // Case where a Retry is needed because the OCR did not produce good enough results and the User should Retry with a better capture.
                    "ID Type Mismatch<br/>Please Try Again" // Case where there is likely no OCR Template installed for the document the User is attempting to scan.
                );
            } else if (scanType === 'back') {
                FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides(
                    "Back Scan Complete<br/>Please Try Again", // Successful scan of ID back-side.
                    "Back of ID Scanned<br/>Please Try Again", // Successful scan of ID back-side.
                    "ID Scan Complete<br/>Please Try Again", // Successful scan of the ID back-side.
                    "Passport Scan Complete<br/>Please Try Again", // Successful scan of a Passport
                    "Photo ID Back Complete<br/>Please Try Again", // Successful upload of final Photo ID Back result containing OCR data.
                    "ID Photo Capture Complete<br/>Please Try Again", // Successful upload of final Photo ID Scan result but scan is flagged as requiring additional review.
                    "Face Didn't Match<br/>Highly Enough", // Case where a Retry is needed because the Face on the Photo ID did not Match the User's Face highly enough.
                    "ID Document<br/>Not Fully Visible", // Case where a Retry is needed because a Full ID was not detected with high enough confidence.
                    "ID Text Not Legible", // Case where a Retry is needed because the OCR did not produce good enough results and the User should Retry with a better capture.
                    "ID Type Mismatch<br/>Please Try Again" // Case where there is likely no OCR Template installed for the document the User is attempting to scan.
                );
            } else {
                // Default/legacy retry messages
                FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides(
                    "Scan Complete<br/>Please Try Again", // Successful scan of ID front-side (ID Types with no back-side).
                    "ID Scanned<br/>Please Try Again", // Successful scan of ID front-side (ID Types that do have a back-side).
                    "ID Scan Complete<br/>Please Try Again", // Successful scan of the ID back-side.
                    "Passport Scan Complete<br/>Please Try Again", // Successful scan of a Passport
                    "Photo ID Scan Complete<br/>Please Try Again", // Successful upload of final Photo ID Scan result containing User-Confirmed ID Text.
                    "ID Photo Capture Complete<br/>Please Try Again", // Successful upload of final Photo ID Scan result but scan is flagged as requiring additional review.
                    "Face Didn't Match<br/>Highly Enough", // Case where a Retry is needed because the Face on the Photo ID did not Match the User's Face highly enough.
                    "ID Document<br/>Not Fully Visible", // Case where a Retry is needed because a Full ID was not detected with high enough confidence.
                    "ID Text Not Legible", // Case where a Retry is needed because the OCR did not produce good enough results and the User should Retry with a better capture.
                    "ID Type Mismatch<br/>Please Try Again" // Case where there is likely no OCR Template installed for the document the User is attempting to scan.
                );
            }
        };

        //
        // Method to show success overlay for back ID card scan with OCR data
        //
        this.showSuccessOverlay = function(ocrData = null) {
            // Validate OCR data if provided
            let validationResult = null;
            if (ocrData) {
                validationResult = OcrDataValidator.validateAll(ocrData);
                DeveloperStatusMessages.logData('OCR Validation Result', validationResult);
            }

            // Create and show success overlay
            const overlay = document.createElement('div');
            overlay.id = 'ekyc-success-overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.9);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                font-family: Arial, sans-serif;
                overflow-y: auto;
                padding: 20px;
                box-sizing: border-box;
            `;

            // Generate OCR fields HTML
            const ocrFieldsHtml = ocrData ? _this.generateOcrFieldsHtml(ocrData, validationResult) : '';

            overlay.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 12px;
                    max-width: 600px;
                    width: 100%;
                    max-height: 90vh;
                    overflow-y: auto;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                ">
                    <!-- Header -->
                    <div style="
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        padding: 24px;
                        border-radius: 12px 12px 0 0;
                        text-align: center;
                        position: relative;
                    ">
                        <h2 style="margin: 0; font-size: 24px; font-weight: 600;">Review & Confirm</h2>
                        <p style="margin: 8px 0 0 0; opacity: 0.9; font-size: 14px;">Information</p>
                        <button onclick="document.getElementById('ekyc-success-overlay').remove()"
                                style="
                                    position: absolute;
                                    top: 16px;
                                    right: 16px;
                                    background: rgba(255,255,255,0.2);
                                    border: none;
                                    color: white;
                                    width: 32px;
                                    height: 32px;
                                    border-radius: 50%;
                                    cursor: pointer;
                                    font-size: 18px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                ">×</button>
                    </div>

                    <!-- Content -->
                    <div style="padding: 24px;">
                        ${ocrFieldsHtml || '<div style="text-align: center; color: #666; padding: 40px;"><div style="font-size: 48px; margin-bottom: 16px;">✓</div><h3 style="margin: 0 0 8px 0;">ID Card Scan Complete</h3><p style="margin: 0; opacity: 0.7;">Back side of ID card successfully scanned</p></div>'}

                        <!-- Action Buttons -->
                        <div style="margin-top: 32px; text-align: center;">
                            <button id="ekyc-confirm-button"
                                    style="
                                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                        color: white;
                                        border: none;
                                        padding: 12px 32px;
                                        border-radius: 8px;
                                        font-size: 16px;
                                        font-weight: 600;
                                        cursor: pointer;
                                        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
                                        transition: transform 0.2s;
                                    "
                                    onmouseover="this.style.transform='translateY(-2px)'"
                                    onmouseout="this.style.transform='translateY(0)'">
                                Confirm Information
                            </button>
                        </div>

                        ${validationResult && validationResult.errors.length > 0 ? `
                        <div style="margin-top: 16px; padding: 12px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px;">
                            <p style="margin: 0; color: #856404; font-size: 14px; font-weight: 600;">Please check your information before submitting</p>
                        </div>
                        ` : ''}
                    </div>
                </div>
            `;

            document.body.appendChild(overlay);

            // Add event listener for confirm button
            const confirmButton = document.getElementById('ekyc-confirm-button');
            if (confirmButton && ocrData) {
                confirmButton.addEventListener('click', function() {
                    _this.handleUserConfirmation(ocrData);
                });
            }

            // Notify the processor that overlay was shown and cancelled
            if (_this._overlayShownAndCancelled) {
                _this._overlayShownAndCancelled();
            }

            faceTecIdScanResultCallback.cancel();

            // Auto-remove after 30 seconds (longer time for OCR review)
            setTimeout(() => {
                if (document.getElementById('ekyc-success-overlay')) {
                    document.getElementById('ekyc-success-overlay').remove();
                }
            }, 30000);
        };

        //
        // Method to generate OCR fields HTML with validation indicators
        //
        this.generateOcrFieldsHtml = function(ocrData, validationResult) {
            const fields = [
                { key: 'nationalId', label: 'National ID Number', required: true },
                { key: 'titleTh', label: 'Title (TH)', required: true },
                { key: 'firstNameTh', label: 'First Name (TH)', required: true },
                { key: 'middleNameTh', label: 'Middle Name (TH)', required: false, note: '(if applicable)' },
                { key: 'lastNameTh', label: 'Last Name (TH)', required: true },
                { key: 'titleEn', label: 'Title (ENG)', required: true },
                { key: 'firstNameEn', label: 'First Name (ENG)', required: true },
                { key: 'middleNameEn', label: 'Middle Name (ENG)', required: false, note: '(if applicable)' },
                { key: 'lastNameEn', label: 'Last Name (ENG)', required: true },
                { key: 'dateOfBirth', label: 'Birth Date', required: true, note: '(A.D. i.e. 2022)', isDate: true },
                { key: 'dateOfIssue', label: 'Issue Date', required: true, note: '(A.D. i.e. 2022)', isDate: true },
                { key: 'dateOfExpiry', label: 'Expiry Date', required: true, note: '(A.D. i.e. 2022)', isDate: true },
                { key: 'laserId', label: 'Laser ID', required: true }
            ];

            let html = '';

            fields.forEach(field => {
                const value = ocrData[field.key] || '';
                const validation = validationResult?.fieldValidations[field.key];
                const status = validation?.status || 'unknown';
                const icon = OcrDataValidator.getStatusIcon(status);
                const color = OcrDataValidator.getStatusColor(status);

                // Format date fields
                let displayValue = value;
                let parts = null;
                if (field.isDate && value) {
                    parts = value.split(/[\/\-\.]/);
                    if (parts.length === 3) {
                        displayValue = `${parts[0]} / ${parts[1]} / ${parts[2]}`;
                    }
                }

                html += `
                    <div style="margin-bottom: 16px;">
                        <label style="
                            display: block;
                            font-size: 14px;
                            font-weight: 600;
                            color: #333;
                            margin-bottom: 6px;
                        ">
                            ${field.label}${field.required ? '*' : ''}
                            ${field.note ? `<span style="font-weight: 400; color: #666; font-size: 12px;"> ${field.note}</span>` : ''}
                            <span style="color: ${color}; margin-left: 8px; font-size: 16px;">${icon}</span>
                        </label>
                        ${field.isDate ? `
                            <div style="display: flex; gap: 8px; align-items: center;">
                                <input type="text" value="${parts && parts[0] ? parts[0] : ''}" placeholder="DD"
                                       style="width: 60px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; text-align: center;" readonly />
                                <span style="color: #666;">/</span>
                                <input type="text" value="${parts && parts[1] ? parts[1] : ''}" placeholder="MM"
                                       style="width: 60px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; text-align: center;" readonly />
                                <span style="color: #666;">/</span>
                                <input type="text" value="${parts && parts[2] ? parts[2] : ''}" placeholder="YYYY"
                                       style="width: 80px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; text-align: center;" readonly />
                            </div>
                        ` : `
                            <input type="text" value="${displayValue}"
                                   style="
                                       width: 100%;
                                       padding: 12px;
                                       border: 1px solid ${status === 'invalid' || status === 'missing' ? '#f44336' : '#ddd'};
                                       border-radius: 6px;
                                       font-size: 14px;
                                       background: ${status === 'missing' ? '#fff3e0' : '#f9f9f9'};
                                       color: #333;
                                   " readonly />
                        `}
                        ${validation && !validation.isValid && validation.error ? `
                            <div style="margin-top: 4px; color: #f44336; font-size: 12px;">
                                ${validation.error}
                            </div>
                        ` : ''}
                    </div>
                `;
            });

            return html;
        };

        //
        // Method to handle user confirmation from success overlay
        //
        this.handleUserConfirmation = function(userOcrValue) {
            DeveloperStatusMessages.logMessage('User confirmed information from overlay');
            DeveloperStatusMessages.logData('User Confirmed OCR Value', userOcrValue);

            // Store callback data for user confirmation
            if (_this.ocrResultsCallback) {
                const originalOcrValue = _this.extractOcrValueForCallback({ userOcrValue: userOcrValue });

                _this.callbackResult = {
                    success: true,
                    description: "User confirmed information",
                    userOcrValue: originalOcrValue,
                    userConfirmedValue: userOcrValue, // User-confirmed data from overlay
                    dopaResult: null,
                    timestamp: new Date().toISOString()
                };

                DeveloperStatusMessages.logMessage('Stored callback result for user confirmation');
                DeveloperStatusMessages.logData('User Confirmation Callback Result', _this.callbackResult);
            }

            // Remove the overlay
            const overlay = document.getElementById('ekyc-success-overlay');
            if (overlay) {
                overlay.remove();
            }
        };

        //
        // Method to get stored callback result
        //
        this.getCallbackResult = function() {
            return _this.callbackResult || null;
        };

        //
        // Method to execute stored callback if available
        //
        this.executeStoredCallback = function() {
            if (_this.ocrResultsCallback && _this.callbackResult) {
                DeveloperStatusMessages.logMessage('Executing stored OCR results callback');
                DeveloperStatusMessages.logData('Callback Result', _this.callbackResult);

                _this.ocrResultsCallback(
                    _this.callbackResult.success,
                    _this.callbackResult.description,
                    _this.callbackResult.userOcrValue,
                    _this.callbackResult.userConfirmedValue,
                    _this.callbackResult.dopaResult
                );

                return true; // Callback was executed
            }
            return false; // No callback to execute
        };

        //
        // Helper method to extract OCR value for callback
        //
        this.extractOcrValueForCallback = function(result) {
            DeveloperStatusMessages.logMessage('Extracting OCR value for callback');
            DeveloperStatusMessages.logData('Input result for OCR extraction', result);

            // If result has userOcrValue, use it directly
            if (result && result.userOcrValue) {
                DeveloperStatusMessages.logMessage('Using existing userOcrValue from result');
                return result.userOcrValue;
            }

            // If result has ocrData, extract from it
            if (result && result.ocrData) {
                DeveloperStatusMessages.logMessage('Extracting OCR data from result.ocrData');
                return _this.extractOcrFromApiResponse(result.ocrData);
            }

            // If result has API response structure, extract from it
            if (result && result.originalResponse && result.originalResponse.data) {
                DeveloperStatusMessages.logMessage('Extracting OCR data from API response structure');
                return _this.extractOcrFromApiResponse(result.originalResponse.data);
            }

            DeveloperStatusMessages.logMessage('No OCR data found in result, returning null');
            return null;
        };

        //
        // Helper method to extract OCR data from API response
        //
        this.extractOcrFromApiResponse = function(apiData) {
            try {
                DeveloperStatusMessages.logMessage('Extracting OCR data from API response');
                DeveloperStatusMessages.logData('API Data', apiData);

                // Initialize OCR value structure
                const ocrValue = {
                    nationalId: null,
                    titleTh: null,
                    firstNameTh: null,
                    middleNameTh: null,
                    lastNameTh: null,
                    titleEn: null,
                    firstNameEn: null,
                    middleNameEn: null,
                    lastNameEn: null,
                    dateOfBirth: null,
                    dateOfIssue: null,
                    dateOfExpiry: null,
                    laserId: null
                };

                // Extract from documentData if available
                if (apiData.documentData) {
                    let documentData;
                    if (typeof apiData.documentData === 'string') {
                        documentData = JSON.parse(apiData.documentData);
                    } else {
                        documentData = apiData.documentData;
                    }

                    if (documentData.scannedValues && documentData.scannedValues.groups) {
                        const groups = documentData.scannedValues.groups;

                        // Extract fields from groups
                        groups.forEach(group => {
                            if (group.fields) {
                                group.fields.forEach(field => {
                                    const fieldKey = field.fieldKey;
                                    const value = field.value || '';

                                    switch (fieldKey) {
                                        case 'nationalId':
                                        case 'idNumber':
                                            ocrValue.nationalId = value;
                                            break;
                                        case 'fullname':
                                            // Extract Thai names from fullname field
                                            const thaiNameParts = value.split(' ').filter(part => part.trim());
                                            if (thaiNameParts.length >= 2) {
                                                ocrValue.titleTh = thaiNameParts[0];
                                                ocrValue.firstNameTh = thaiNameParts[1];
                                                if (thaiNameParts.length > 2) {
                                                    ocrValue.lastNameTh = thaiNameParts.slice(2).join(' ');
                                                }
                                            }
                                            break;
                                        case 'firstName':
                                            // Extract English names from firstName field
                                            const engNameParts = value.split(' ').filter(part => part.trim());
                                            if (engNameParts.length >= 1) {
                                                ocrValue.titleEn = engNameParts[0];
                                                if (engNameParts.length > 1) {
                                                    ocrValue.firstNameEn = engNameParts.slice(1).join(' ');
                                                }
                                            }
                                            break;
                                        case 'lastName':
                                        case 'lastNameEn':
                                            ocrValue.lastNameEn = value;
                                            break;
                                        case 'dateOfBirth':
                                            ocrValue.dateOfBirth = _this.convertThaiDateToStandard(value);
                                            break;
                                        case 'dateOfIssue':
                                            ocrValue.dateOfIssue = _this.convertThaiDateToStandard(value);
                                            break;
                                        case 'dateOfExpiry':
                                            ocrValue.dateOfExpiry = _this.convertThaiDateToStandard(value);
                                            break;
                                        case 'laserId':
                                            ocrValue.laserId = value;
                                            break;
                                    }
                                });
                            }
                        });
                    }
                }

                DeveloperStatusMessages.logMessage('OCR extraction completed');
                DeveloperStatusMessages.logData('Extracted OCR Value', ocrValue);

                return ocrValue;
            } catch (error) {
                DeveloperStatusMessages.logMessage('Error extracting OCR data: ' + error.message);
                console.error('Error extracting OCR data:', error);
                return null;
            }
        };

        //
        // Helper method to convert Thai date format to standard dd/MM/yyyy format
        //
        this.convertThaiDateToStandard = function(thaiDate) {
            if (!thaiDate) return null;

            try {
                // Thai month names mapping
                const thaiMonths = {
                    'ม.ค.': '01', 'ก.พ.': '02', 'มี.ค.': '03', 'เม.ย.': '04',
                    'พ.ค.': '05', 'มิ.ย.': '06', 'ก.ค.': '07', 'ส.ค.': '08',
                    'ก.ย.': '09', 'ต.ค.': '10', 'พ.ย.': '11', 'ธ.ค.': '12'
                };

                // Match pattern: dd MMM. yyyy
                const match = thaiDate.match(/(\d{1,2})\s+([ก-ฮ\.]+)\s+(\d{4})/);
                if (match) {
                    const day = match[1].padStart(2, '0');
                    const thaiMonth = match[2];
                    const year = match[3];

                    const month = thaiMonths[thaiMonth];
                    if (month) {
                        return `${day}/${month}/${year}`;
                    }
                }

                // If no match, return original value
                return thaiDate;
            } catch (error) {
                console.error('Error converting Thai date:', error);
                return thaiDate;
            }
        };

        //
        // Part 18:  This function gets called after the FaceTec SDK is completely done.  There are no parameters because you have already been passed all data in the processSessionWhileFaceTecSDKWaits function and have already handled all of your own results.
        //
        this.onFaceTecSDKCompletelyDone = function () {
            //
            // DEVELOPER NOTE:  onFaceTecSDKCompletelyDone() is called after the Session has completed or you signal the FaceTec SDK with cancel().
            // Calling a custom function on the Sample App Controller is done for demonstration purposes to show you that here is where you get control back from the FaceTec SDK.
            //
            // If the Photo ID Scan was processed get the success result from isCompletelyDone
            if (_this.latestIDScanResult != null) {
                _this.success = _this.latestIDScanResult.isCompletelyDone;
            }
            // Log success message
            // if (_this.success) {
            //     DeveloperStatusMessages.logMessage("Id Scan Complete");
            // }
            // // If enrollment was not successful, clear the enrollment identifier
            // else {
            //     _this.sampleAppControllerReference.clearLatestEnrollmentIdentifier();
            // }
            _this.sampleAppControllerReference.onComplete(_this.latestSessionResult, _this.latestIDScanResult, _this.latestNetworkRequest.status);
        };
        // Helper function to ensure the session is only cancelled once
        this.cancelDueToNetworkError = function (networkErrorMessage, faceTecScanResultCallback) {
            if (_this.cancelledDueToNetworkError === false) {
                console.error(networkErrorMessage);
                _this.cancelledDueToNetworkError = true;
                faceTecScanResultCallback.cancel();
            }
        };
        //
        // DEVELOPER NOTE:  This public convenience method is for demonstration purposes only so the Sample App can get information about what is happening in the processor.
        // In your code, you may not even want or need to do this.
        //
        this.isSuccess = function () {
            return _this.success;
        };
        //
        // DEVELOPER NOTE:  These properties are for demonstration purposes only so the Sample App can get information about what is happening in the processor.
        // In the code in your own App, you can pass around signals, flags, intermediates, and results however you would like.
        //
        this.success = false;
        this.sampleAppControllerReference = sampleAppControllerReference;
        this.latestSessionResult = null;
        this.latestIDScanResult = null;
        this.cancelledDueToNetworkError = false;
        // In v9.2.2+, configure the messages that will be displayed to the User in each of the possible cases.
        // Based on the internal processing and decision logic about how the flow gets advanced, the FaceTec SDK will use the appropriate, configured message.
        FaceTecSDK.FaceTecCustomization.setIDScanUploadMessageOverrides("Uploading<br/>Encrypted<br/>ID Scan", // Upload of ID front-side has started.
        "Still Uploading...<br/>Slow Connection", // Upload of ID front-side is still uploading to Server after an extended period of time.
        "Upload Complete", // Upload of ID front-side to the Server is complete.
        "Processing<br/>ID Scan", // Upload of ID front-side is complete and we are waiting for the Server to finish processing and respond.
        "Uploading<br/>Encrypted<br/>Back of ID", // Upload of ID back-side has started.
        "Still Uploading...<br/>Slow Connection", // Upload of ID back-side is still uploading to Server after an extended period of time.
        "Upload Complete", // Upload of ID back-side to Server is complete.
        "Processing<br/>Back of ID", // Upload of ID back-side is complete and we are waiting for the Server to finish processing and respond.
        "Uploading<br/>Your Confirmed Info", // Upload of User Confirmed Info has started.
        "Still Uploading...<br/>Slow Connection", // Upload of User Confirmed Info is still uploading to Server after an extended period of time.
        "Info Saved", // Upload of User Confirmed Info to the Server is complete.
        "Processing" // Upload of User Confirmed Info is complete and we are waiting for the Server to finish processing and respond.
        );
        //
        // Part 1:  Starting the FaceTec Session
        //
        // Required parameters:
        // - FaceTecIDScanProcessor:  A class that implements FaceTecIDScanProcessor, which handles the IDScan when the User completes an ID Scan.  In this example, "this" implements the class.
        // - sessionToken:  A valid Session Token you just created by calling your API to get a Session Token from the Server SDK.
        //
        new FaceTecSDK.FaceTecSession(this, sessionToken);
    }
    return PhotoIDMatchProcessor;
}());
var PhotoIDMatchProcessor = PhotoIDMatchProcessor;

// Export the PhotoIDMatchProcessor class for use in Node.js/webpack environments
module.exports = PhotoIDMatchProcessor;
