const FaceTecRepository = require('../../data/repositories/FaceTecRepository');
const UuidGenerator = require('../../infrastructure/utils/UuidGenerator');
const DeveloperStatusMessages = require('../../infrastructure/utils/DeveloperStatusMessages');

/**
 * Use case for processing back ID card scans
 * Handles the business logic for submitting back ID card data to the eKYC API
 * Includes full OCR data extraction and processing capabilities
 */
class PostIDScanBackUseCase {
    constructor() {
        this.faceTecRepository = new FaceTecRepository();
    }

    /**
     * Execute the back ID scan submission process
     * @param {Object} params - Parameters for the use case
     * @param {Object} params.idScanResult - FaceTec ID scan result
     * @param {string} params.deviceKey - Device key for authentication
     * @param {Object} params.additionalHeaders - Additional headers for the request
     * @param {Function} params.onProgress - Progress callback function
     * @returns {Promise<Object>} - Use case result
     */
    async execute({ idScanResult, deviceKey, additionalHeaders = {}, onProgress = null }) {
        const startTime = performance.now();
        
        try {
            DeveloperStatusMessages.logMessage('Starting PostIDScanBackUseCase execution');
            
            // Prepare scan data from FaceTec result for back scan
            DeveloperStatusMessages.logMessage('Preparing back scan data...');
            const scanData = this.prepareBackScanData(idScanResult);
            DeveloperStatusMessages.logData('Back Scan Data Keys', Object.keys(scanData));
            
            // Prepare headers
            DeveloperStatusMessages.logMessage('Preparing headers...');
            const headers = this.prepareHeaders(idScanResult, deviceKey, additionalHeaders);
            DeveloperStatusMessages.logData('Request Headers', Object.keys(headers));
            
            // Validate scan data
            DeveloperStatusMessages.logMessage('Validating back scan data...');
            this.faceTecRepository.validateBackScanData(scanData);
            DeveloperStatusMessages.logSuccess('Back scan data validation passed');
            
            // Submit to repository with progress tracking
            DeveloperStatusMessages.logMessage('Submitting back scan to repository...');
            const response = await this.faceTecRepository.submitIDScanBack(scanData, headers, onProgress);
            
            // Process the response according to business rules
            DeveloperStatusMessages.logMessage('Processing back scan response...');
            const result = this.processResponse(response);
            
            const endTime = performance.now();
            DeveloperStatusMessages.logPerformance('PostIDScanBackUseCase.execute', startTime, endTime);
            DeveloperStatusMessages.logSuccess(`Back UseCase completed successfully: ${result.success}`);
            
            return result;
        } catch (error) {
            const endTime = performance.now();
            DeveloperStatusMessages.logPerformance('PostIDScanBackUseCase.execute (failed)', startTime, endTime);
            DeveloperStatusMessages.logError('PostIDScanBackUseCase execution failed:', error);
            throw error;
        }
    }

    /**
     * Prepare back scan data from FaceTec ID scan result
     * @param {Object} idScanResult - FaceTec ID scan result
     * @returns {Object} - Prepared back scan data
     */
    prepareBackScanData(idScanResult) {
        const parameters = {
            idScan: idScanResult.idScan,
            enableConfirmInfo: true // default value follow spec
        };

        // Add back image (required for back scan)
        if (idScanResult.backImages && idScanResult.backImages[0]) {
            parameters.idScanBackImage = idScanResult.backImages[0];
        } else {
            throw new Error('Back image is required for back ID scan processing');
        }

        // Include front image if available for complete processing
        if (idScanResult.frontImages && idScanResult.frontImages[0]) {
            parameters.idScanFrontImage = idScanResult.frontImages[0];
        }

        return parameters;
    }

    /**
     * Prepare headers for the API request
     * @param {Object} idScanResult - FaceTec ID scan result
     * @param {string} deviceKey - Device key for authentication
     * @param {Object} additionalHeaders - Additional headers from the processor
     * @returns {Object} - Prepared headers
     */
    prepareHeaders(idScanResult, deviceKey, additionalHeaders) {
        // Start with additional headers (following PostLivenessCheckUseCase pattern)
        const headers = { ...additionalHeaders };

        // Add device key if provided
        if (deviceKey) {
            headers['X-Device-Key'] = deviceKey;
        }

        // Add FaceTec user agent header
        if (idScanResult.sessionId && typeof FaceTecSDK !== 'undefined') {
            headers['X-User-Agent'] = FaceTecSDK.createFaceTecAPIUserAgentString(idScanResult.sessionId);
        }

        // Ensure X-Tid is present (generate if not provided in additionalHeaders)
        if (!headers['X-Tid']) {
            headers['X-Tid'] = UuidGenerator.getUniqueId();
            DeveloperStatusMessages.logData('Generated X-Tid header for back scan', headers['X-Tid']);
        } else {
            DeveloperStatusMessages.logData('X-Tid header already present for back scan', headers['X-Tid']);
        }

        // Add required content type headers
        headers['Content-Type'] = 'application/json';
        headers['Accept'] = 'application/json';

        // Remove undefined headers
        Object.keys(headers).forEach(key => {
            if (headers[key] === undefined) {
                delete headers[key];
            }
        });

        DeveloperStatusMessages.logData('Back scan prepared headers', Object.keys(headers));
        return headers;
    }

    /**
     * Process the API response according to business rules
     * @param {Object} response - API response
     * @returns {Object} - Processed response
     */
    processResponse(response) {
        // Business logic for processing back scan response
        // Back scans contain full OCR data with Thai date conversion and field metadata

        // Extract OCR data from the original response if available
        let userOcrValue = null;
        if (response.originalResponse && response.originalResponse.data && response.originalResponse.data.documentData) {
            try {
                // Parse the JSON string in documentData
                const documentData = JSON.parse(response.originalResponse.data.documentData);
                DeveloperStatusMessages.logData('Parsed back scan documentData', documentData);

                // Extract OCR data from the nested structure
                userOcrValue = this.extractBackOcrDataFromDocumentData(documentData);
                
                if (userOcrValue) {
                    DeveloperStatusMessages.logData('Extracted Back OCR Data', Object.keys(userOcrValue));
                } else {
                    DeveloperStatusMessages.logMessage('No valid back OCR fields found in documentData');
                }
            } catch (error) {
                DeveloperStatusMessages.logError('Failed to parse back scan documentData JSON:', error);
                DeveloperStatusMessages.logMessage('No back OCR data found in response');
            }
        } else {
            DeveloperStatusMessages.logMessage('No documentData found in back scan response');
        }

        return {
            success: response.wasProcessed === true && response.error === false,
            scanResultBlob: response.scanResultBlob,
            originalResponse: response.originalResponse,
            errorMessage: response.errorMessage,
            userOcrValue: userOcrValue,
            scanType: 'back'
        };
    }

    /**
     * Extract full OCR data from back scan documentData structure
     * @param {Object} documentData - Parsed documentData object
     * @returns {Object|null} - Extracted back OCR data or null if no valid data found
     */
    extractBackOcrDataFromDocumentData(documentData) {
        if (!documentData || !documentData.scannedValues || !documentData.scannedValues.groups) {
            DeveloperStatusMessages.logMessage('Invalid back scan documentData structure');
            return null;
        }

        // Initialize OCR data object with all expected fields
        const ocrData = {
            nationalId: null,
            titleTh: null,
            firstNameTh: null,
            middleNameTh: null,
            lastNameTh: null,
            titleEn: null,
            firstNameEn: null,
            middleNameEn: null,
            lastNameEn: null,
            dateOfBirth: null,
            dateOfIssue: null,
            dateOfExpiry: null,
            laserId: null
        };

        // Field mapping configuration - includes raw field extraction
        const fieldMapping = {
            'idNumber': 'nationalId',
            'firstName': '_rawFirstName', // Store raw value for parsing
            'fullname': '_rawFullname',   // Store raw value for parsing
            'fullName': '_rawFullname',   // Alternative field name for fullname
            'lastName': 'lastNameEn',
            'dateOfBirth': 'dateOfBirth',
            'dateOfIssue': 'dateOfIssue',
            'dateOfExpiration': 'dateOfExpiry',
            'customField1': 'laserId'
        };

        // Temporary storage for raw values that need parsing
        const rawData = {
            _rawFirstName: null,
            _rawFullname: null
        };

        // Iterate through groups and fields to extract data
        documentData.scannedValues.groups.forEach(group => {
            if (group.fields && Array.isArray(group.fields)) {
                group.fields.forEach(field => {
                    if (field.fieldKey && field.value && fieldMapping[field.fieldKey]) {
                        const mappedField = fieldMapping[field.fieldKey];
                        
                        if (mappedField.startsWith('_raw')) {
                            // Store raw values for parsing
                            rawData[mappedField] = field.value;
                            DeveloperStatusMessages.logData(`Stored raw ${field.fieldKey}`, field.value);
                        } else {
                            // Direct mapping
                            ocrData[mappedField] = field.value;
                            DeveloperStatusMessages.logData(`Mapped ${field.fieldKey} -> ${mappedField}`, field.value);
                        }
                    }
                });
            }
        });

        // Parse Thai fields from fullname
        if (rawData._rawFullname) {
            this.parseThaiNameFields(rawData._rawFullname, ocrData);
        }

        // Parse English fields from firstName
        if (rawData._rawFirstName) {
            this.parseEnglishNameFields(rawData._rawFirstName, ocrData);
        }

        // Convert date fields from Thai format to standard format
        this.convertDateFields(ocrData);

        // Add field editability metadata
        this.addFieldEditabilityMetadata(ocrData);

        // Check if we found any valid data
        const hasValidData = Object.values(ocrData).some(value => value !== null);
        
        if (!hasValidData) {
            DeveloperStatusMessages.logMessage('No valid back OCR fields found in documentData structure');
            return null;
        }

        return ocrData;
    }

    /**
     * Parse Thai name fields from the fullname string
     * @param {string} fullname - The full Thai name string
     * @param {Object} ocrData - The OCR data object to populate
     */
    parseThaiNameFields(fullname, ocrData) {
        if (!fullname || typeof fullname !== 'string') {
            DeveloperStatusMessages.logMessage('Invalid fullname for Thai parsing');
            return;
        }

        const nameParts = fullname.trim().split(/\s+/);
        DeveloperStatusMessages.logData('Thai name parts', nameParts);

        if (nameParts.length >= 1) {
            // titleTh = 1st word
            ocrData.titleTh = nameParts[0];
            DeveloperStatusMessages.logData('Extracted titleTh', ocrData.titleTh);
        }

        if (nameParts.length >= 2) {
            // firstNameTh = 2nd word
            ocrData.firstNameTh = nameParts[1];
            DeveloperStatusMessages.logData('Extracted firstNameTh', ocrData.firstNameTh);
        }

        if (nameParts.length >= 3) {
            // lastNameTh = all remaining words (3rd word onwards)
            ocrData.lastNameTh = nameParts.slice(2).join(' ');
            DeveloperStatusMessages.logData('Extracted lastNameTh', ocrData.lastNameTh);
        }

        // middleNameTh remains null as it's optional
    }

    /**
     * Parse English name fields from the firstName string
     * @param {string} firstName - The English firstName string (may include title)
     * @param {Object} ocrData - The OCR data object to populate
     */
    parseEnglishNameFields(firstName, ocrData) {
        if (!firstName || typeof firstName !== 'string') {
            DeveloperStatusMessages.logMessage('Invalid firstName for English parsing');
            return;
        }

        const nameParts = firstName.trim().split(/\s+/);
        DeveloperStatusMessages.logData('English name parts', nameParts);

        if (nameParts.length >= 1) {
            // titleEn = 1st word
            ocrData.titleEn = nameParts[0];
            DeveloperStatusMessages.logData('Extracted titleEn', ocrData.titleEn);
        }

        if (nameParts.length >= 2) {
            // firstNameEn = all remaining words (2nd word onwards)
            ocrData.firstNameEn = nameParts.slice(1).join(' ');
            DeveloperStatusMessages.logData('Extracted firstNameEn', ocrData.firstNameEn);
        }

        // middleNameEn remains null as it's optional
    }

    /**
     * Convert date fields from Thai format to standard dd/MM/yyyy format
     * @param {Object} ocrData - The OCR data object to process
     */
    convertDateFields(ocrData) {
        const dateFields = ['dateOfBirth', 'dateOfIssue', 'dateOfExpiry'];

        dateFields.forEach(fieldName => {
            if (ocrData[fieldName]) {
                const convertedDate = this.convertThaiDateFormat(ocrData[fieldName]);
                if (convertedDate) {
                    DeveloperStatusMessages.logData(`Converted ${fieldName}`, `${ocrData[fieldName]} -> ${convertedDate}`);
                    ocrData[fieldName] = convertedDate;
                } else {
                    DeveloperStatusMessages.logError(`Failed to convert date field ${fieldName}`, ocrData[fieldName]);
                }
            }
        });
    }

    /**
     * Convert Thai date format to standard dd/MM/yyyy format
     * @param {string} thaiDate - Date in Thai format (e.g., "15 ม.ค. 2567")
     * @returns {string|null} - Date in dd/MM/yyyy format or null if conversion fails
     */
    convertThaiDateFormat(thaiDate) {
        if (!thaiDate || typeof thaiDate !== 'string') {
            return null;
        }

        try {
            // Thai month abbreviations mapping
            const thaiMonths = {
                'ม.ค.': '01', 'มกราคม': '01', 'ม.ค': '01',
                'ก.พ.': '02', 'กุมภาพันธ์': '02', 'ก.พ': '02',
                'มี.ค.': '03', 'มีนาคม': '03', 'มี.ค': '03',
                'เม.ย.': '04', 'เมษายน': '04', 'เม.ย': '04',
                'พ.ค.': '05', 'พฤษภาคม': '05', 'พ.ค': '05',
                'มิ.ย.': '06', 'มิถุนายน': '06', 'มิ.ย': '06',
                'ก.ค.': '07', 'กรกฎาคม': '07', 'ก.ค': '07',
                'ส.ค.': '08', 'สิงหาคม': '08', 'ส.ค': '08',
                'ก.ย.': '09', 'กันยายน': '09', 'ก.ย': '09',
                'ต.ค.': '10', 'ตุลาคม': '10', 'ต.ค': '10',
                'พ.ย.': '11', 'พฤศจิกายน': '11', 'พ.ย': '11',
                'ธ.ค.': '12', 'ธันวาคม': '12', 'ธ.ค': '12'
            };

            // Clean the input and split into parts
            const cleanDate = thaiDate.trim();

            // Check if already in dd/MM/yyyy format
            if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(cleanDate)) {
                DeveloperStatusMessages.logData('Date already in standard format', cleanDate);
                return cleanDate;
            }

            // Parse Thai format: "dd MMM. yyyy" or "dd MMM yyyy"
            const parts = cleanDate.split(/\s+/);

            if (parts.length >= 3) {
                const day = parts[0].padStart(2, '0');
                const monthThai = parts[1];
                const year = parts[2];

                // Find matching month
                const month = thaiMonths[monthThai];

                if (month && /^\d{4}$/.test(year)) {
                    const convertedDate = `${day}/${month}/${year}`;
                    DeveloperStatusMessages.logData('Thai date conversion successful', `${thaiDate} -> ${convertedDate}`);
                    return convertedDate;
                }
            }

            // Try alternative parsing for different formats
            // Handle format like "15/ม.ค./2567"
            const slashParts = cleanDate.split('/');
            if (slashParts.length === 3) {
                const day = slashParts[0].padStart(2, '0');
                const monthThai = slashParts[1];
                const year = slashParts[2];

                const month = thaiMonths[monthThai];
                if (month && /^\d{4}$/.test(year)) {
                    const convertedDate = `${day}/${month}/${year}`;
                    DeveloperStatusMessages.logData('Thai date conversion (slash format) successful', `${thaiDate} -> ${convertedDate}`);
                    return convertedDate;
                }
            }

            DeveloperStatusMessages.logError('Unable to parse Thai date format', thaiDate);
            return null;

        } catch (error) {
            DeveloperStatusMessages.logError('Error converting Thai date format', error);
            return null;
        }
    }

    /**
     * Add field editability metadata to OCR data
     * @param {Object} ocrData - The OCR data object to enhance
     */
    addFieldEditabilityMetadata(ocrData) {
        // Define which fields should be editable
        const editableFields = [
            'titleTh', 'firstNameTh', 'lastNameTh',
            'titleEn', 'firstNameEn', 'lastNameEn',
            'dateOfBirth', 'dateOfIssue', 'dateOfExpiry'
        ];

        const readOnlyFields = [
            'nationalId', 'laserId'
        ];

        // Add metadata object to track field properties
        ocrData._fieldMetadata = {
            editableFields: editableFields,
            readOnlyFields: readOnlyFields,
            dateFields: ['dateOfBirth', 'dateOfIssue', 'dateOfExpiry'],
            requiredFields: ['nationalId', 'titleTh', 'firstNameTh', 'lastNameTh', 'dateOfBirth', 'dateOfExpiry'],
            optionalFields: ['middleNameTh', 'middleNameEn', 'titleEn', 'firstNameEn', 'lastNameEn', 'dateOfIssue', 'laserId']
        };

        // Add individual field properties
        Object.keys(ocrData).forEach(fieldName => {
            if (fieldName !== '_fieldMetadata' && ocrData[fieldName] !== null) {
                // Create field property object
                const fieldKey = `_${fieldName}Properties`;
                ocrData[fieldKey] = {
                    isEditable: editableFields.includes(fieldName),
                    isReadOnly: readOnlyFields.includes(fieldName),
                    isDate: ['dateOfBirth', 'dateOfIssue', 'dateOfExpiry'].includes(fieldName),
                    isRequired: ['nationalId', 'titleTh', 'firstNameTh', 'lastNameTh', 'dateOfBirth', 'dateOfExpiry'].includes(fieldName),
                    originalValue: ocrData[fieldName],
                    hasBeenModified: false
                };
            }
        });

        DeveloperStatusMessages.logData('Added field editability metadata', {
            editableCount: editableFields.length,
            readOnlyCount: readOnlyFields.length,
            dateFieldCount: 3
        });
    }
}

module.exports = PostIDScanBackUseCase;
