const FaceTecRepository = require('../../data/repositories/FaceTecRepository');
const UuidGenerator = require('../../infrastructure/utils/UuidGenerator');
const DeveloperStatusMessages = require('../../infrastructure/utils/DeveloperStatusMessages');

/**
 * Use case for processing front ID card scans
 * Handles the business logic for submitting front ID card data to the eKYC API
 */
class PostIDScanFrontUseCase {
    constructor() {
        this.faceTecRepository = new FaceTecRepository();
    }

    /**
     * Execute the front ID scan submission process
     * @param {Object} params - Parameters for the use case
     * @param {Object} params.idScanResult - FaceTec ID scan result
     * @param {string} params.deviceKey - Device key for authentication
     * @param {Object} params.additionalHeaders - Additional headers for the request
     * @param {Function} params.onProgress - Progress callback function
     * @returns {Promise<Object>} - Use case result
     */
    async execute({ idScanResult, deviceKey, additionalHeaders = {}, onProgress = null }) {
        const startTime = performance.now();
        
        try {
            DeveloperStatusMessages.logMessage('Starting PostIDScanFrontUseCase execution');
            
            // Prepare scan data from FaceTec result for front scan
            DeveloperStatusMessages.logMessage('Preparing front scan data...');
            const scanData = this.prepareFrontScanData(idScanResult);
            DeveloperStatusMessages.logData('Front Scan Data Keys', Object.keys(scanData));
            
            // Prepare headers
            DeveloperStatusMessages.logMessage('Preparing headers...');
            const headers = this.prepareHeaders(idScanResult, deviceKey, additionalHeaders);
            DeveloperStatusMessages.logData('Request Headers', Object.keys(headers));
            
            // Validate scan data
            DeveloperStatusMessages.logMessage('Validating front scan data...');
            this.faceTecRepository.validateFrontScanData(scanData);
            DeveloperStatusMessages.logSuccess('Front scan data validation passed');
            
            // Submit to repository with progress tracking
            DeveloperStatusMessages.logMessage('Submitting front scan to repository...');
            const response = await this.faceTecRepository.submitIDScanFront(scanData, headers, onProgress);
            
            // Process the response according to business rules
            DeveloperStatusMessages.logMessage('Processing front scan response...');
            const result = this.processResponse(response);
            
            const endTime = performance.now();
            DeveloperStatusMessages.logPerformance('PostIDScanFrontUseCase.execute', startTime, endTime);
            DeveloperStatusMessages.logSuccess(`Front UseCase completed successfully: ${result.success}`);
            
            return result;
        } catch (error) {
            const endTime = performance.now();
            DeveloperStatusMessages.logPerformance('PostIDScanFrontUseCase.execute (failed)', startTime, endTime);
            DeveloperStatusMessages.logError('PostIDScanFrontUseCase execution failed:', error);
            throw error;
        }
    }

    /**
     * Prepare front scan data from FaceTec ID scan result
     * @param {Object} idScanResult - FaceTec ID scan result
     * @returns {Object} - Prepared front scan data
     */
    prepareFrontScanData(idScanResult) {
        const parameters = {
            idScan: idScanResult.idScan,
            enableConfirmInfo: true // default value follow spec
        };

        // Add front image (required for front scan)
        if (idScanResult.frontImages && idScanResult.frontImages[0]) {
            parameters.idScanFrontImage = idScanResult.frontImages[0];
        } else {
            throw new Error('Front image is required for front ID scan processing');
        }

        return parameters;
    }

    /**
     * Prepare headers for the API request
     * @param {Object} idScanResult - FaceTec ID scan result
     * @param {string} deviceKey - Device key for authentication
     * @param {Object} additionalHeaders - Additional headers from the processor
     * @returns {Object} - Prepared headers
     */
    prepareHeaders(idScanResult, deviceKey, additionalHeaders) {
        // Start with additional headers (following PostLivenessCheckUseCase pattern)
        const headers = { ...additionalHeaders };

        // Add device key if provided
        if (deviceKey) {
            headers['X-Device-Key'] = deviceKey;
        }

        // Add FaceTec user agent header
        if (idScanResult.sessionId && typeof FaceTecSDK !== 'undefined') {
            headers['X-User-Agent'] = FaceTecSDK.createFaceTecAPIUserAgentString(idScanResult.sessionId);
        }

        // Ensure X-Tid is present (generate if not provided in additionalHeaders)
        if (!headers['X-Tid']) {
            headers['X-Tid'] = UuidGenerator.getUniqueId();
            DeveloperStatusMessages.logData('Generated X-Tid header for front scan', headers['X-Tid']);
        } else {
            DeveloperStatusMessages.logData('X-Tid header already present for front scan', headers['X-Tid']);
        }

        // Add required content type headers
        headers['Content-Type'] = 'application/json';
        headers['Accept'] = 'application/json';

        // Remove undefined headers
        Object.keys(headers).forEach(key => {
            if (headers[key] === undefined) {
                delete headers[key];
            }
        });

        DeveloperStatusMessages.logData('Front scan prepared headers', Object.keys(headers));
        return headers;
    }

    /**
     * Process the API response according to business rules
     * @param {Object} response - API response
     * @returns {Object} - Processed response
     */
    processResponse(response) {
        // Business logic for processing front scan response
        // Front scans typically don't contain full OCR data, mainly validation

        // Extract basic data from the original response if available
        let userOcrValue = null;
        if (response.originalResponse && response.originalResponse.data && response.originalResponse.data.documentData) {
            try {
                // Parse the JSON string in documentData
                const documentData = JSON.parse(response.originalResponse.data.documentData);
                DeveloperStatusMessages.logData('Parsed front scan documentData', documentData);

                // Extract basic OCR data from the nested structure (limited for front scan)
                userOcrValue = this.extractFrontOcrDataFromDocumentData(documentData);
                
                if (userOcrValue) {
                    DeveloperStatusMessages.logData('Extracted Front OCR Data', Object.keys(userOcrValue));
                } else {
                    DeveloperStatusMessages.logMessage('No valid front OCR fields found in documentData');
                }
            } catch (error) {
                DeveloperStatusMessages.logError('Failed to parse front scan documentData JSON:', error);
                DeveloperStatusMessages.logMessage('No front OCR data found in response');
            }
        } else {
            DeveloperStatusMessages.logMessage('No documentData found in front scan response');
        }

        return {
            success: response.wasProcessed === true && response.error === false,
            scanResultBlob: response.scanResultBlob,
            originalResponse: response.originalResponse,
            errorMessage: response.errorMessage,
            userOcrValue: userOcrValue,
            scanType: 'front'
        };
    }

    /**
     * Extract limited OCR data from front scan documentData structure
     * @param {Object} documentData - Parsed documentData object
     * @returns {Object|null} - Extracted front OCR data or null if no valid data found
     */
    extractFrontOcrDataFromDocumentData(documentData) {
        if (!documentData || !documentData.scannedValues || !documentData.scannedValues.groups) {
            DeveloperStatusMessages.logMessage('Invalid front scan documentData structure');
            return null;
        }

        // Initialize front OCR data object (limited fields for front scan)
        const ocrData = {
            nationalId: null,
            // Front scans typically contain basic identification info
            scanType: 'front',
            scanQuality: null
        };

        // Field mapping configuration for front scan
        const fieldMapping = {
            'idNumber': 'nationalId',
            'quality': 'scanQuality'
        };

        // Iterate through groups and fields to extract front scan data
        documentData.scannedValues.groups.forEach(group => {
            if (group.fields && Array.isArray(group.fields)) {
                group.fields.forEach(field => {
                    if (field.fieldKey && field.value && fieldMapping[field.fieldKey]) {
                        const ocrFieldName = fieldMapping[field.fieldKey];
                        ocrData[ocrFieldName] = field.value;
                        DeveloperStatusMessages.logData(`Front scan mapped ${field.fieldKey} -> ${ocrFieldName}`, field.value);
                    }
                });
            }
        });

        // Check if we found any valid data
        const hasValidData = Object.values(ocrData).some(value => value !== null);
        
        if (!hasValidData) {
            DeveloperStatusMessages.logMessage('No valid front OCR fields found in documentData structure');
            return null;
        }

        return ocrData;
    }
}

module.exports = PostIDScanFrontUseCase;
